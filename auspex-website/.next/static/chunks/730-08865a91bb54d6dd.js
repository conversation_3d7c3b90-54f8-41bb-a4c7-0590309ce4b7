"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[730],{5523:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(7043)._(n(2265)).default.createContext(null)},6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},8575:function(e,t,n){n.d(t,{F:function(){return i},e:function(){return o}});var r=n(2265);function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=u(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():u(e[t],null)}}}}function o(...e){return r.useCallback(i(...e),e)}},3966:function(e,t,n){n.d(t,{b:function(){return o},k:function(){return i}});var r=n(2265),u=n(7437);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,o=r.useMemo(()=>i,Object.values(i));return(0,u.jsx)(n.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(u){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${u}\` must be used within \`${e}\``)}]}function o(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let u=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:u}}),[n,u])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),l=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,s=n?.[e]?.[l]||o,c=r.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:c,children:i})};return a.displayName=t+"Provider",[a,function(n,u){let a=u?.[e]?.[l]||o,s=r.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let u=n.reduce((t,{useScope:n,scopeName:r})=>{let u=n(e)[`__scope${r}`];return{...t,...u}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return n.scopeName=t.scopeName,n}(i,...t)]}},5278:function(e,t,n){n.d(t,{I0:function(){return E},XB:function(){return f},fC:function(){return y}});var r,u=n(2265),i=n(6741),o=n(2912),l=n(8575),a=n(6606),s=n(7437),c="dismissableLayer.update",d=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=u.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:y,onPointerDownOutside:E,onFocusOutside:b,onInteractOutside:h,onDismiss:w,...g}=e,N=u.useContext(d),[C,O]=u.useState(null),P=null!==(f=null==C?void 0:C.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,W]=u.useState({}),L=(0,l.e)(t,e=>O(e)),R=Array.from(N.layers),[j]=[...N.layersWithOutsidePointerEventsDisabled].slice(-1),x=R.indexOf(j),T=C?R.indexOf(C):-1,M=N.layersWithOutsidePointerEventsDisabled.size>0,D=T>=x,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),i=u.useRef(!1),o=u.useRef(()=>{});return u.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,u,{discrete:!0})},u={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...N.branches].some(e=>e.contains(t));!D||n||(null==E||E(e),null==h||h(e),e.defaultPrevented||null==w||w())},P),A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),i=u.useRef(!1);return u.useEffect(()=>{let e=e=>{e.target&&!i.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...N.branches].some(e=>e.contains(t))||(null==b||b(e),null==h||h(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let n=(0,a.W)(e);u.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T!==N.layers.size-1||(null==y||y(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),u.useEffect(()=>{if(C)return v&&(0===N.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),N.layersWithOutsidePointerEventsDisabled.add(C)),N.layers.add(C),m(),()=>{v&&1===N.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[C,P,v,N]),u.useEffect(()=>()=>{C&&(N.layers.delete(C),N.layersWithOutsidePointerEventsDisabled.delete(C),m())},[C,N]),u.useEffect(()=>{let e=()=>W({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(o.WV.div,{...g,ref:L,style:{pointerEvents:M?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,A.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,k.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=u.forwardRef((e,t)=>{let n=u.useContext(d),r=u.useRef(null),i=(0,l.e)(t,r);return u.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(o.WV.div,{...e,ref:i})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:u}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),u?(0,o.jH)(i,l):i.dispatchEvent(l)}v.displayName="DismissableLayerBranch";var y=f,E=v},3832:function(e,t,n){n.d(t,{h:function(){return a}});var r=n(2265),u=n(4887),i=n(2912),o=n(1188),l=n(7437),a=r.forwardRef((e,t)=>{var n,a;let{container:s,...c}=e,[d,f]=r.useState(!1);(0,o.b)(()=>f(!0),[]);let v=s||d&&(null===(a=globalThis)||void 0===a?void 0:null===(n=a.document)||void 0===n?void 0:n.body);return v?u.createPortal((0,l.jsx)(i.WV.div,{...c,ref:t}),v):null});a.displayName="Portal"},1599:function(e,t,n){n.d(t,{z:function(){return o}});var r=n(2265),u=n(8575),i=n(1188),o=e=>{var t,n;let o,a;let{present:s,children:c}=e,d=function(e){var t,n;let[u,o]=r.useState(),a=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(a.current);c.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,u=l(t);e?f("MOUNT"):"none"===u||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==u?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.b)(()=>{if(u){var e;let t;let n=null!==(e=u.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(a.current).includes(e.animationName);if(e.target===u&&r&&(f("ANIMATION_END"),!s.current)){let e=u.style.animationFillMode;u.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===u.style.animationFillMode&&(u.style.animationFillMode=e)})}},i=e=>{e.target===u&&(c.current=l(a.current))};return u.addEventListener("animationstart",i),u.addEventListener("animationcancel",r),u.addEventListener("animationend",r),()=>{n.clearTimeout(t),u.removeEventListener("animationstart",i),u.removeEventListener("animationcancel",r),u.removeEventListener("animationend",r)}}f("ANIMATION_END")},[u,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),o(e)},[])}}(s),f="function"==typeof c?c({present:d.isPresent}):r.Children.only(c),v=(0,u.e)(d.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||d.isPresent?r.cloneElement(f,{ref:v}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},2912:function(e,t,n){n.d(t,{WV:function(){return d},jH:function(){return f}});var r=n(2265),u=n(4887),i=n(8575),o=n(7437),l=r.forwardRef((e,t)=>{let{children:n,...u}=e,i=r.Children.toArray(n),l=i.find(c);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(a,{...u,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,o.jsx)(a,{...u,ref:t,children:n})});l.displayName="Slot";var a=r.forwardRef((e,t)=>{let{children:n,...u}=e;if(r.isValidElement(n)){let e,o;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let u=e[r],i=t[r];/^on[A-Z]/.test(r)?u&&i?n[r]=(...e)=>{i(...e),u(...e)}:u&&(n[r]=u):"style"===r?n[r]={...u,...i}:"className"===r&&(n[r]=[u,i].filter(Boolean).join(" "))}return{...e,...n}}(u,n.props);return n.type!==r.Fragment&&(a.ref=t?(0,i.F)(t,l):l),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});a.displayName="SlotClone";var s=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function c(e){return r.isValidElement(e)&&e.type===s}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...u}=e,i=r?l:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...u,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function f(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},6606:function(e,t,n){n.d(t,{W:function(){return u}});var r=n(2265);function u(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},886:function(e,t,n){n.d(t,{T:function(){return i}});var r=n(2265),u=n(6606);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,o]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,o=r.useRef(i),l=(0,u.W)(t);return r.useEffect(()=>{o.current!==i&&(l(i),o.current=i)},[i,o,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,a=l?e:i,s=(0,u.W)(n);return[a,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else o(t)},[l,e,o,s])]}},1188:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),u=globalThis?.document?r.useLayoutEffect:()=>{}},5098:function(e,t,n){n.d(t,{T:function(){return o},f:function(){return l}});var r=n(2265),u=n(2912),i=n(7437),o=r.forwardRef((e,t)=>(0,i.jsx)(u.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));o.displayName="VisuallyHidden";var l=o},535:function(e,t,n){n.d(t,{j:function(){return o}});var r=n(1994);let u=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:l}=t,a=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=u(t)||u(r);return o[e][i]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...u}=t;return Object.entries(u).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...s}[t]):({...l,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},6471:function(e,t,n){n.d(t,{Z:function(){return a}});var r=n(2265);let u=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:u=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...o,width:u,height:u,stroke:n,strokeWidth:a?24*Number(l)/Number(u):l,className:i("lucide",s),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),a=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:a,...s}=n;return(0,r.createElement)(l,{ref:o,iconNode:t,className:i("lucide-".concat(u(e)),a),...s})});return n.displayName="".concat(e),n}}}]);