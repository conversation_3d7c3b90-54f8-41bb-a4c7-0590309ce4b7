"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[154],{3145:function(e,t,n){n.d(t,{default:function(){return i.a}});var r=n(8461),i=n.n(r)},5878:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let r=n(7043),i=n(3099),o=n(7437),l=i._(n(2265)),a=r._(n(4887)),u=r._(n(8293)),s=n(5346),c=n(128),f=n(2589);n(1765);let d=n(5523),p=r._(n(5084)),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,n,r,i,o,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,i=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function m(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let y=(0,l.forwardRef)((e,t)=>{let{src:n,srcSet:r,sizes:i,height:a,width:u,decoding:s,className:c,style:f,fetchPriority:d,placeholder:p,loading:h,unoptimized:y,fill:v,onLoadRef:x,onLoadingCompleteRef:w,setBlurComplete:b,setShowAltText:C,sizesInput:E,onLoad:R,onError:S,...j}=e;return(0,o.jsx)("img",{...j,...m(d),loading:h,width:u,height:a,decoding:s,"data-nimg":v?"fill":"1",className:c,style:f,sizes:i,srcSet:r,src:n,ref:(0,l.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(S&&(e.src=e.src),e.complete&&g(e,p,x,w,b,y,E))},[n,p,x,w,b,S,y,E,t]),onLoad:e=>{g(e.currentTarget,p,x,w,b,y,E)},onError:e=>{C(!0),"empty"!==p&&b(!0),S&&S(e)}})});function v(e){let{isAppRouter:t,imgAttributes:n}=e,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...m(n.fetchPriority)};return t&&a.default.preload?(a.default.preload(n.src,r),null):(0,o.jsx)(u.default,{children:(0,o.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let x=(0,l.forwardRef)((e,t)=>{let n=(0,l.useContext)(d.RouterContext),r=(0,l.useContext)(f.ImageConfigContext),i=(0,l.useMemo)(()=>{var e;let t=h||r||c.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:i,qualities:o}},[r]),{onLoad:a,onLoadingComplete:u}=e,g=(0,l.useRef)(a);(0,l.useEffect)(()=>{g.current=a},[a]);let m=(0,l.useRef)(u);(0,l.useEffect)(()=>{m.current=u},[u]);let[x,w]=(0,l.useState)(!1),[b,C]=(0,l.useState)(!1),{props:E,meta:R}=(0,s.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:x,showAltText:b});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y,{...E,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:g,onLoadingCompleteRef:m,setBlurComplete:w,setShowAltText:C,sizesInput:e.sizes,ref:t}),R.priority?(0,o.jsx)(v,{isAppRouter:!n,imgAttributes:E}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1436:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=n(7043)._(n(2265)).default.createContext({})},3964:function(e,t){function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||n&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},5346:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),n(1765);let r=n(6496),i=n(128);function o(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var n,a;let u,s,c,{src:f,sizes:d,unoptimized:p=!1,priority:h=!1,loading:g,className:m,quality:y,width:v,height:x,fill:w=!1,style:b,overrideSrc:C,onLoad:E,onLoadingComplete:R,placeholder:S="empty",blurDataURL:j,fetchPriority:P,decoding:_="async",layout:O,objectFit:A,objectPosition:T,lazyBoundary:M,lazyRoot:k,...L}=e,{imgConf:z,showAltText:D,blurComplete:I,defaultLoader:W}=t,N=z||i.imageConfigDefault;if("allSizes"in N)u=N;else{let e=[...N.deviceSizes,...N.imageSizes].sort((e,t)=>e-t),t=N.deviceSizes.sort((e,t)=>e-t),r=null==(n=N.qualities)?void 0:n.sort((e,t)=>e-t);u={...N,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===W)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let F=L.loader||W;delete L.loader,delete L.srcSet;let H="__next_img_default"in F;if(H){if("custom"===u.loader)throw Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=F;F=t=>{let{config:n,...r}=t;return e(r)}}if(O){"fill"===O&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!d&&(d=t)}let V="",B=l(v),U=l(x);if("object"==typeof(a=f)&&(o(a)||void 0!==a.src)){let e=o(f)?f.default:f;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(s=e.blurWidth,c=e.blurHeight,j=j||e.blurDataURL,V=e.src,!w){if(B||U){if(B&&!U){let t=B/e.width;U=Math.round(e.height*t)}else if(!B&&U){let t=U/e.height;B=Math.round(e.width*t)}}else B=e.width,U=e.height}}let Y=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:V)||f.startsWith("data:")||f.startsWith("blob:"))&&(p=!0,Y=!1),u.unoptimized&&(p=!0),H&&f.endsWith(".svg")&&!u.dangerouslyAllowSVG&&(p=!0),h&&(P="high");let q=l(y),G=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:T}:{},D?{}:{color:"transparent"},b),$=I||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:B,heightInt:U,blurWidth:s,blurHeight:c,blurDataURL:j||"",objectFit:G.objectFit})+'")':'url("'+S+'")',X=$?{backgroundSize:G.objectFit||"cover",backgroundPosition:G.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},Z=function(e){let{config:t,src:n,unoptimized:r,width:i,quality:o,sizes:l,loader:a}=e;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:u,kind:s}=function(e,t,n){let{deviceSizes:r,allSizes:i}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(n);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,l),c=u.length-1;return{sizes:l||"w"!==s?l:"100vw",srcSet:u.map((e,r)=>a({config:t,src:n,quality:o,width:e})+" "+("w"===s?e:r+1)+s).join(", "),src:a({config:t,src:n,quality:o,width:u[c]})}}({config:u,src:f,unoptimized:p,width:B,quality:q,sizes:d,loader:F});return{props:{...L,loading:Y?"lazy":g,fetchPriority:P,width:B,height:U,decoding:_,className:m,style:{...G,...X},sizes:Z.sizes,srcSet:Z.srcSet,src:C||Z.src},meta:{unoptimized:p,priority:h,placeholder:S,fill:w}}}},8293:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},defaultHead:function(){return f}});let r=n(7043),i=n(3099),o=n(7437),l=i._(n(2265)),a=r._(n(7421)),u=n(1436),s=n(8701),c=n(3964);function f(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(1765);let p=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:n}=t;return e.reduce(d,[]).reverse().concat(f(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return i=>{let o=!0,l=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){l=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=i.props[t],n=r[t]||new Set;("name"!==t||!l)&&n.has(e)?o=!1:(n.add(e),r[t]=n)}}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,l.default.cloneElement(e,t)}return l.default.cloneElement(e,{key:r})})}let g=function(e){let{children:t}=e,n=(0,l.useContext)(u.AmpStateContext),r=(0,l.useContext)(s.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:h,headManager:r,inAmpMode:(0,c.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6496:function(e,t){function n(e){let{widthInt:t,heightInt:n,blurWidth:r,blurHeight:i,blurDataURL:o,objectFit:l}=e,a=r?40*r:t,u=i?40*i:n,s=a&&u?"viewBox='0 0 "+a+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},2589:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let r=n(7043)._(n(2265)),i=n(128),o=r.default.createContext(i.imageConfigDefault)},128:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8461:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return u},getImageProps:function(){return a}});let r=n(7043),i=n(5346),o=n(5878),l=r._(n(5084));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let u=o.Image},5084:function(e,t){function n(e){var t;let{config:n,src:r,width:i,quality:o}=e,l=o||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+l}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},7421:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=n(2265),i="undefined"==typeof window,o=i?()=>{}:r.useLayoutEffect,l=i?()=>{}:r.useEffect;function a(e){let{headManager:t,reduceComponentsToState:n}=e;function a(){if(t&&t.mountedInstances){let i=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(i,e))}}if(i){var u;null==t||null==(u=t.mountedInstances)||u.add(e.children),a()}return o(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),l(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},9255:function(e,t,n){n.d(t,{M:function(){return u}});var r,i=n(2265),o=n(1188),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function u(e){let[t,n]=i.useState(l());return(0,o.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},8482:function(e,t,n){n.d(t,{g7:function(){return l}});var r=n(2265);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=n(7437),l=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,a=r.Children.toArray(i),s=a.find(u);if(s){let e=s.props.children,i=a.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}("Slot"),a=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},8999:function(e,t,n){n.d(t,{VY:function(){return tx},zt:function(){return tm},fC:function(){return ty},xz:function(){return tv}});var r=n(2265),i=n(6741),o=n(8575),l=n(3966),a=n(5278),u=n(9255);let s=["top","right","bottom","left"],c=Math.min,f=Math.max,d=Math.round,p=Math.floor,h=e=>({x:e,y:e}),g={left:"right",right:"left",bottom:"top",top:"bottom"},m={start:"end",end:"start"};function y(e,t){return"function"==typeof e?e(t):e}function v(e){return e.split("-")[0]}function x(e){return e.split("-")[1]}function w(e){return"x"===e?"y":"x"}function b(e){return"y"===e?"height":"width"}function C(e){return["top","bottom"].includes(v(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>m[e])}function R(e){return e.replace(/left|right|bottom|top/g,e=>g[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function j(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function P(e,t,n){let r,{reference:i,floating:o}=e,l=C(t),a=w(C(t)),u=b(a),s=v(t),c="y"===l,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,p=i[u]/2-o[u]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(x(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let _=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=P(s,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:g}=a[n],{x:m,y:y,data:v,reset:x}=await g({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=m?m:c,f=null!=y?y:f,p={...p,[o]:{...p[o],...v}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:c,y:f}=P(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=y(t,e),h=S(p),g=a[d?"floating"===f?"reference":"floating":f],m=j(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),v="floating"===f?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),w=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=j(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:x,strategy:u}):v);return{top:(m.top-b.top+h.top)/w.y,bottom:(b.bottom-m.bottom+h.bottom)/w.y,left:(m.left-b.left+h.left)/w.x,right:(b.right-m.right+h.right)/w.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return s.some(t=>e[t]>=0)}async function M(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=v(n),a=x(n),u="y"===C(n),s=["left","top"].includes(l)?-1:1,c=o&&u?-1:1,f=y(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*c,y:d*s}:{x:d*s,y:p*c}}function k(){return"undefined"!=typeof window}function L(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return!!k()&&(e instanceof Node||e instanceof z(e).Node)}function W(e){return!!k()&&(e instanceof Element||e instanceof z(e).Element)}function N(e){return!!k()&&(e instanceof HTMLElement||e instanceof z(e).HTMLElement)}function F(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof z(e).ShadowRoot)}function H(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=q(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function V(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=U(),n=W(e)?q(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function U(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Y(e){return["html","body","#document"].includes(L(e))}function q(e){return z(e).getComputedStyle(e)}function G(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||D(e);return F(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=$(t);return Y(n)?t.ownerDocument?t.ownerDocument.body:t.body:N(n)&&H(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=z(i);if(o){let e=Z(l);return t.concat(l,l.visualViewport||[],H(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function J(e){let t=q(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=N(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,a=d(n)!==o||d(r)!==l;return a&&(n=o,r=l),{width:n,height:r,$:a}}function K(e){return W(e)?e:e.contextElement}function Q(e){let t=K(e);if(!N(t))return h(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=J(t),l=(o?d(n.width):n.width)/r,a=(o?d(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ee=h(0);function et(e){let t=z(e);return U()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function en(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=K(e),a=h(1);t&&(r?W(r)&&(a=Q(r)):a=Q(e));let u=(void 0===(i=n)&&(i=!1),r&&(!i||r===z(l))&&i)?et(l):h(0),s=(o.left+u.x)/a.x,c=(o.top+u.y)/a.y,f=o.width/a.x,d=o.height/a.y;if(l){let e=z(l),t=r&&W(r)?z(r):r,n=e,i=Z(n);for(;i&&r&&t!==n;){let e=Q(i),t=i.getBoundingClientRect(),r=q(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,f*=e.x,d*=e.y,s+=o,c+=l,i=Z(n=z(i))}}return j({width:f,height:d,x:s,y:c})}function er(e,t){let n=G(e).scrollLeft;return t?t.left+n:en(D(e)).left+n}function ei(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:er(e,r)),y:r.top+t.scrollTop}}function eo(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=z(e),r=D(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,u=0;if(i){o=i.width,l=i.height;let e=U();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=G(e),r=e.ownerDocument.body,i=f(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=f(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+er(e),a=-n.scrollTop;return"rtl"===q(r).direction&&(l+=f(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:l,y:a}}(D(e));else if(W(t))r=function(e,t){let n=en(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=N(e)?Q(e):h(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=et(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return j(r)}function el(e){return"static"===q(e).position}function ea(e,t){if(!N(e)||"fixed"===q(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function eu(e,t){let n=z(e);if(V(e))return n;if(!N(e)){let t=$(e);for(;t&&!Y(t);){if(W(t)&&!el(t))return t;t=$(t)}return n}let r=ea(e,t);for(;r&&["table","td","th"].includes(L(r))&&el(r);)r=ea(r,t);return r&&Y(r)&&el(r)&&!B(r)?n:r||function(e){let t=$(e);for(;N(t)&&!Y(t);){if(B(t))return t;if(V(t))break;t=$(t)}return null}(e)||n}let es=async function(e){let t=this.getOffsetParent||eu,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=N(t),i=D(t),o="fixed"===n,l=en(e,!0,o,t),a={scrollLeft:0,scrollTop:0},u=h(0);if(r||!r&&!o){if(("body"!==L(t)||H(i))&&(a=G(t)),r){let e=en(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else i&&(u.x=er(i))}let s=!i||r||o?h(0):ei(i,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ec={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=D(r),a=!!t&&V(t.floating);if(r===l||a&&o)return n;let u={scrollLeft:0,scrollTop:0},s=h(1),c=h(0),f=N(r);if((f||!f&&!o)&&(("body"!==L(r)||H(l))&&(u=G(r)),N(r))){let e=en(r);s=Q(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||o?h(0):ei(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+d.x,y:n.y*s.y-u.scrollTop*s.y+c.y+d.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>W(e)&&"body"!==L(e)),i=null,o="fixed"===q(e).position,l=o?$(e):e;for(;W(l)&&!Y(l);){let t=q(l),n=B(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||H(l)&&!n&&function e(t,n){let r=$(t);return!(r===n||!W(r)||Y(r))&&("fixed"===q(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=$(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=o[0],a=o.reduce((e,n)=>{let r=eo(t,n,i);return e.top=f(r.top,e.top),e.right=c(r.right,e.right),e.bottom=c(r.bottom,e.bottom),e.left=f(r.left,e.left),e},eo(t,l,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eu,getElementRects:es,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=J(e);return{width:t,height:n}},getScale:Q,isElement:W,isRTL:function(e){return"rtl"===q(e).direction}};function ef(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:d=0}=y(e,t)||{};if(null==s)return{};let p=S(d),h={x:n,y:r},g=w(C(i)),m=b(g),v=await l.getDimensions(s),E="y"===g,R=E?"clientHeight":"clientWidth",j=o.reference[m]+o.reference[g]-h[g]-o.floating[m],P=h[g]-o.reference[g],_=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),O=_?_[R]:0;O&&await (null==l.isElement?void 0:l.isElement(_))||(O=a.floating[R]||o.floating[m]);let A=O/2-v[m]/2-1,T=c(p[E?"top":"left"],A),M=c(p[E?"bottom":"right"],A),k=O-v[m]-M,L=O/2-v[m]/2+(j/2-P/2),z=f(T,c(L,k)),D=!u.arrow&&null!=x(i)&&L!==z&&o.reference[m]/2-(L<T?T:M)-v[m]/2<0,I=D?L<T?L-T:L-k:0;return{[g]:h[g]+I,data:{[g]:z,centerOffset:L-z-I,...D&&{alignmentOffset:I}},reset:D}}}),ep=(e,t,n)=>{let r=new Map,i={platform:ec,...n},o={...i.platform,_c:r};return _(e,t,{...i,platform:o})};var eh=n(4887),eg="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function em(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!em(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!em(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ey(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ev(e,t){let n=ey(e);return Math.round(t*n)/n}function ex(e){let t=r.useRef(e);return eg(()=>{t.current=e}),t}let ew=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ed({element:n.current,padding:r}).fn(t):{}:n?ed({element:n,padding:r}).fn(t):{}}}),eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:l,middlewareData:a}=e,u=await M(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}),options:[e,t]}},eC=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:o=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=y(n,e),s={x:t,y:r},d=await O(e,u),p=C(v(i)),h=w(p),g=s[h],m=s[p];if(o){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=g+d[e],r=g-d[t];g=f(n,c(g,r))}if(l){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=m+d[e],r=m-d[t];m=f(n,c(m,r))}let x=a.fn({...e,[h]:g,[p]:m});return{...x,data:{x:x.x-t,y:x.y-r,enabled:{[h]:o,[p]:l}}}}}),options:[e,t]}},eE=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=y(n,e),c={x:t,y:r},f=C(i),d=w(f),p=c[d],h=c[f],g=y(a,e),m="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(u){let e="y"===d?"height":"width",t=o.reference[d]-o.floating[e]+m.mainAxis,n=o.reference[d]+o.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var x,b;let e="y"===d?"width":"height",t=["top","left"].includes(v(i)),n=o.reference[f]-o.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:m.crossAxis),r=o.reference[f]+o.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[f])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}),options:[e,t]}},eR=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:d}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:g,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:j=!0,...P}=y(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let _=v(a),A=C(c),T=v(c)===c,M=await (null==f.isRTL?void 0:f.isRTL(d.floating)),k=g||(T||!j?[R(c)]:function(e){let t=R(e);return[E(e),t,E(t)]}(c)),L="none"!==S;!g&&L&&k.push(...function(e,t,n,r){let i=x(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(v(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(E)))),o}(c,j,S,M));let z=[c,...k],D=await O(e,P),I=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&I.push(D[_]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=x(e),i=w(C(e)),o=b(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=R(l)),[l,R(l)]}(a,s,M);I.push(D[e[0]],D[e[1]])}if(W=[...W,{placement:a,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=z[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(o=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=W.filter(e=>{if(L){let t=C(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eS=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let i,o;let{placement:l,rects:a,platform:u,elements:s}=e,{apply:d=()=>{},...p}=y(n,e),h=await O(e,p),g=v(l),m=x(l),w="y"===C(l),{width:b,height:E}=a.floating;"top"===g||"bottom"===g?(i=g,o=m===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(o=g,i="end"===m?"top":"bottom");let R=E-h.top-h.bottom,S=b-h.left-h.right,j=c(E-h[i],R),P=c(b-h[o],S),_=!e.middlewareData.shift,A=j,T=P;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(T=S),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(A=R),_&&!m){let e=f(h.left,0),t=f(h.right,0),n=f(h.top,0),r=f(h.bottom,0);w?T=b-2*(0!==e||0!==t?e+t:f(h.left,h.right)):A=E-2*(0!==n||0!==r?n+r:f(h.top,h.bottom))}await d({...e,availableWidth:T,availableHeight:A});let M=await u.getDimensions(s.floating);return b!==M.width||E!==M.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},ej=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=y(n,e);switch(r){case"referenceHidden":{let n=A(await O(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:T(n)}}}case"escaped":{let n=A(await O(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:T(n)}}}default:return{}}}}),options:[e,t]}},eP=(e,t)=>({...ew(e),options:[e,t]});var e_=n(2912),eO=n(7437),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eO.jsx)(e_.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eT=n(6606),eM=n(1188),ek="Popper",[eL,ez]=(0,l.b)(ek),[eD,eI]=eL(ek),eW=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eO.jsx)(eD,{scope:t,anchor:i,onAnchorChange:o,children:n})};eW.displayName=ek;var eN="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...l}=e,a=eI(eN,n),u=r.useRef(null),s=(0,o.e)(t,u);return r.useEffect(()=>{a.onAnchorChange((null==i?void 0:i.current)||u.current)}),i?null:(0,eO.jsx)(e_.WV.div,{...l,ref:s})});eF.displayName=eN;var eH="PopperContent",[eV,eB]=eL(eH),eU=r.forwardRef((e,t)=>{var n,i,l,a,u,s,d,h;let{__scopePopper:g,side:m="bottom",sideOffset:y=0,align:v="center",alignOffset:x=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:C=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:j="optimized",onPlaced:P,..._}=e,O=eI(eH,g),[A,T]=r.useState(null),M=(0,o.e)(t,e=>T(e)),[k,L]=r.useState(null),z=function(e){let[t,n]=r.useState(void 0);return(0,eM.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(k),I=null!==(d=null==z?void 0:z.width)&&void 0!==d?d:0,W=null!==(h=null==z?void 0:z.height)&&void 0!==h?h:0,N="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},F=Array.isArray(C)?C:[C],H=F.length>0,V={padding:N,boundary:F.filter(e$),altBoundary:H},{refs:B,floatingStyles:U,placement:Y,isPositioned:q,middlewareData:G}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);em(p,i)||h(i);let[g,m]=r.useState(null),[y,v]=r.useState(null),x=r.useCallback(e=>{e!==E.current&&(E.current=e,m(e))},[]),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=l||g,C=a||y,E=r.useRef(null),R=r.useRef(null),S=r.useRef(f),j=null!=s,P=ex(s),_=ex(o),O=ex(c),A=r.useCallback(()=>{if(!E.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};_.current&&(e.platform=_.current),ep(E.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};T.current&&!em(S.current,t)&&(S.current=t,eh.flushSync(()=>{d(t)}))})},[p,t,n,_,O]);eg(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let T=r.useRef(!1);eg(()=>(T.current=!0,()=>{T.current=!1}),[]),eg(()=>{if(b&&(E.current=b),C&&(R.current=C),b&&C){if(P.current)return P.current(b,C,A);A()}},[b,C,A,P,j]);let M=r.useMemo(()=>({reference:E,floating:R,setReference:x,setFloating:w}),[x,w]),k=r.useMemo(()=>({reference:b,floating:C}),[b,C]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=ev(k.floating,f.x),r=ev(k.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ey(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:A,refs:M,elements:k,floatingStyles:L}),[f,A,M,k,L])}({strategy:"fixed",placement:m+("center"!==v?"-"+v:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,d=K(e),h=o||l?[...d?X(d):[],...X(t)]:[];h.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let g=d&&u?function(e,t){let n,r=null,i=D(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),o();let s=e.getBoundingClientRect(),{left:d,top:h,width:g,height:m}=s;if(a||t(),!g||!m)return;let y=p(h),v=p(i.clientWidth-(d+g)),x={rootMargin:-y+"px "+-v+"px "+-p(i.clientHeight-(h+m))+"px "+-p(d)+"px",threshold:f(0,c(1,u))||1},w=!0;function b(t){let r=t[0].intersectionRatio;if(r!==u){if(!w)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||ef(s,e.getBoundingClientRect())||l(),w=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),o}(d,n):null,m=-1,y=null;a&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),d&&!s&&y.observe(d),y.observe(t));let v=s?en(e):null;return s&&function t(){let r=en(e);v&&!ef(v,r)&&n(),v=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{o&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==g||g(),null==(e=y)||e.disconnect(),y=null,s&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===j})},elements:{reference:O.anchor},middleware:[eb({mainAxis:y+W,alignmentAxis:x}),b&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eE():void 0,...V}),b&&eR({...V}),eS({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),k&&eP({element:k,padding:w}),eX({arrowWidth:I,arrowHeight:W}),S&&ej({strategy:"referenceHidden",...V})]}),[$,Z]=eZ(Y),J=(0,eT.W)(P);(0,eM.b)(()=>{q&&(null==J||J())},[q,J]);let Q=null===(n=G.arrow)||void 0===n?void 0:n.x,ee=null===(i=G.arrow)||void 0===i?void 0:i.y,et=(null===(l=G.arrow)||void 0===l?void 0:l.centerOffset)!==0,[er,ei]=r.useState();return(0,eM.b)(()=>{A&&ei(window.getComputedStyle(A).zIndex)},[A]),(0,eO.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:q?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(a=G.transformOrigin)||void 0===a?void 0:a.x,null===(u=G.transformOrigin)||void 0===u?void 0:u.y].join(" "),...(null===(s=G.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eV,{scope:g,placedSide:$,onArrowChange:L,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eO.jsx)(e_.WV.div,{"data-side":$,"data-align":Z,..._,ref:M,style:{..._.style,animation:q?void 0:"none"}})})})});eU.displayName=eH;var eY="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eB(eY,n),o=eq[i.placedSide];return(0,eO.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function e$(e){return null!==e}eG.displayName=eY;var eX=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=eZ(a),g={start:"0%",center:"50%",end:"100%"}[h],m=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,y=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,v="",x="";return"bottom"===p?(v=c?g:"".concat(m,"px"),x="".concat(-d,"px")):"top"===p?(v=c?g:"".concat(m,"px"),x="".concat(u.floating.height+d,"px")):"right"===p?(v="".concat(-d,"px"),x=c?g:"".concat(y,"px")):"left"===p&&(v="".concat(u.floating.width+d,"px"),x=c?g:"".concat(y,"px")),{data:{x:v,y:x}}}});function eZ(e){let[t,n="center"]=e.split("-");return[t,n]}n(3832);var eJ=n(1599);r.forwardRef((e,t)=>{let{children:n,...i}=e,o=r.Children.toArray(n),l=o.find(e0);if(l){let e=l.props.children,n=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,eO.jsx)(eK,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,eO.jsx)(eK,{...i,ref:t,children:n})}).displayName="Slot";var eK=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,o.F)(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});eK.displayName="SlotClone";var eQ=({children:e})=>(0,eO.jsx)(eO.Fragment,{children:e});function e0(e){return r.isValidElement(e)&&e.type===eQ}var e1=n(886),e2=n(5098),[e5,e4]=(0,l.b)("Tooltip",[ez]),e3=ez(),e8="TooltipProvider",e6="tooltip.open",[e7,e9]=e5(e8),te=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:l}=e,[a,u]=r.useState(!0),s=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,eO.jsx)(e7,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),u(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u(!0),i)},[i]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:o,children:l})};te.displayName=e8;var tt="Tooltip",[tn,tr]=e5(tt),ti=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:s}=e,c=e9(tt,e.__scopeTooltip),f=e3(t),[d,p]=r.useState(null),h=(0,u.M)(),g=r.useRef(0),m=null!=a?a:c.disableHoverableContent,y=null!=s?s:c.delayDuration,v=r.useRef(!1),[x=!1,w]=(0,e1.T)({prop:i,defaultProp:o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(e6))):c.onClose(),null==l||l(e)}}),b=r.useMemo(()=>x?v.current?"delayed-open":"instant-open":"closed",[x]),C=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,v.current=!1,w(!0)},[w]),E=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,w(!1)},[w]),R=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{v.current=!0,w(!0),g.current=0},y)},[y,w]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,eO.jsx)(eW,{...f,children:(0,eO.jsx)(tn,{scope:t,contentId:h,open:x,stateAttribute:b,trigger:d,onTriggerChange:p,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayed?R():C()},[c.isOpenDelayed,R,C]),onTriggerLeave:r.useCallback(()=>{m?E():(window.clearTimeout(g.current),g.current=0)},[E,m]),onOpen:C,onClose:E,disableHoverableContent:m,children:n})})};ti.displayName=tt;var to="TooltipTrigger",tl=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=tr(to,n),u=e9(to,n),s=e3(n),c=r.useRef(null),f=(0,o.e)(t,c,a.onTriggerChange),d=r.useRef(!1),p=r.useRef(!1),h=r.useCallback(()=>d.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,eO.jsx)(eF,{asChild:!0,...s,children:(0,eO.jsx)(e_.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||p.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),p.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),p.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:(0,i.M)(e.onBlur,a.onClose),onClick:(0,i.M)(e.onClick,a.onClose)})})});tl.displayName=to;var[ta,tu]=e5("TooltipPortal",{forceMount:void 0}),ts="TooltipContent",tc=r.forwardRef((e,t)=>{let n=tu(ts,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,l=tr(ts,e.__scopeTooltip);return(0,eO.jsx)(eJ.z,{present:r||l.open,children:l.disableHoverableContent?(0,eO.jsx)(th,{side:i,...o,ref:t}):(0,eO.jsx)(tf,{side:i,...o,ref:t})})}),tf=r.forwardRef((e,t)=>{let n=tr(ts,e.__scopeTooltip),i=e9(ts,e.__scopeTooltip),l=r.useRef(null),a=(0,o.e)(t,l),[u,s]=r.useState(null),{trigger:c,onClose:f}=n,d=l.current,{onPointerInTransitChange:p}=i,h=r.useCallback(()=>{s(null),p(!1)},[p]),g=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&d){let e=e=>g(e,d),t=e=>g(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,g,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let l=t[e].x,a=t[e].y,u=t[o].x,s=t[o].y;a>r!=s>r&&n<(u-l)*(r-a)/(s-a)+l&&(i=!i)}return i}(n,u);r?h():i&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,u,f,h]),(0,eO.jsx)(th,{...e,ref:a})}),[td,tp]=e5(tt,{isInside:!1}),th=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:l,onPointerDownOutside:u,...s}=e,c=tr(ts,n),f=e3(n),{onClose:d}=c;return r.useEffect(()=>(document.addEventListener(e6,d),()=>document.removeEventListener(e6,d)),[d]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,d]),(0,eO.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:(0,eO.jsxs)(eU,{"data-state":c.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eO.jsx)(eQ,{children:i}),(0,eO.jsx)(td,{scope:n,isInside:!0,children:(0,eO.jsx)(e2.f,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tc.displayName=ts;var tg="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=e3(n);return tp(tg,n).isInside?null:(0,eO.jsx)(eG,{...i,...r,ref:t})}).displayName=tg;var tm=te,ty=ti,tv=tl,tx=tc},9354:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(6471).Z)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])}}]);