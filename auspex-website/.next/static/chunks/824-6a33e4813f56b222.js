"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[824],{1994:function(t,e,i){i.d(e,{W:function(){return r}});function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r)}return n}(t))&&(r&&(r+=" "),r+=e);return r}},9637:function(t,e,i){i.d(e,{oO:function(){return s}});var r=i(2265),n=i(4252);function s(t=!0){let e=(0,r.useContext)(n.O);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:o}=e,a=(0,r.useId)();(0,r.useEffect)(()=>{t&&o(a)},[t]);let l=(0,r.useCallback)(()=>t&&s&&s(a),[a,s,t]);return!i&&s?[!1,l]:[!0]}},8881:function(t,e,i){i.d(e,{p:function(){return r}});let r=(0,i(2265).createContext)({})},5750:function(t,e,i){i.d(e,{_:function(){return r}});let r=(0,i(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},4252:function(t,e,i){i.d(e,{O:function(){return r}});let r=(0,i(2265).createContext)(null)},521:function(t,e,i){let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{E:function(){return sg}});let s=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,r){if("function"==typeof e){let[n,s]=l(r);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=l(r);e=e(void 0!==i?i:t.custom,n,s)}return e}function h(t,e,i){let r=t.getProps();return u(r,e,void 0!==i?i:r.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class g extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function v(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function b(t){return"function"==typeof t}function x(t,e){t.timeline=e,t.onfinish=null}let w=t=>Array.isArray(t)&&"number"==typeof t[0],P={linearEasing:void 0},T=function(t,e){let i=p(t);return()=>{var t;return null!==(t=P[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),S=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r},A=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(S(0,n-1,e))+", ";return`linear(${r.substring(0,r.length-2)})`},k=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:k([0,.65,.55,1]),circOut:k([.55,0,1,.45]),backIn:k([.31,.01,.66,-.59]),backOut:k([.33,1.53,.69,.99])},E={x:!1,y:!1};function V(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function C(t){return e=>{"touch"===e.pointerType||E.x||E.y||t(e)}}let D=(t,e)=>!!e&&(t===e||D(t,e.parentElement)),R=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,j=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function F(t){return e=>{"Enter"===e.key&&t(e)}}function B(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let O=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=F(()=>{if(L.has(i))return;B(i,"down");let t=F(()=>{B(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>B(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function I(t){return R(t)&&!(E.x||E.y)}let z=t=>1e3*t,U=t=>t/1e3,N=t=>t,$=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],W=new Set($),Y=new Set(["width","height","top","left","right","bottom",...$]),H=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),G=t=>s(t)?t[t.length-1]||0:t,X={skipAnimations:!1,useManualTiming:!1},K=["read","resolveKeyframes","update","preRender","render","postRender"];function q(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=K.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,r=!1,n=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){s.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&r?e:i;return n&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),s.delete(t)},process:t=>{if(o=t,r){n=!0;return}r=!0,[e,i]=[i,e],e.forEach(a),e.clear(),r=!1,n&&(n=!1,l.process(t))}};return l}(s),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let s=X.useManualTiming?n.timestamp:performance.now();i=!1,n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(p))},m=()=>{i=!0,r=!0,n.isProcessing||t(p)};return{schedule:K.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,n=!1)=>(i||m(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<K.length;e++)o[K[e]].cancel(t)},state:n,steps:o}}let{schedule:_,cancel:Z,state:J,steps:Q}=q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:N,!0);function tt(){r=void 0}let te={now:()=>(void 0===r&&te.set(J.isProcessing||X.useManualTiming?J.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function tr(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tn{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>tr(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ts=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ts(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tn);let i=this.events[t].add(e);return"change"===t?()=>{i(),_.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let td=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tc="data-"+td("framerAppearId"),tp={current:!1},tm=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tf(t,e,i,r){if(t===e&&i===r)return N;let n=e=>(function(t,e,i,r,n){let s,o;let a=0;do(s=tm(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tm(n(t),e,r)}let tg=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tv=t=>e=>1-t(1-e),ty=tf(.33,1.53,.69,.99),tb=tv(ty),tx=tg(tb),tw=t=>(t*=2)<1?.5*tb(t):.5*(2-Math.pow(2,-10*(t-1))),tP=t=>1-Math.sin(Math.acos(t)),tT=tv(tP),tS=tg(tP),tA=t=>/^0[^.\s]+$/u.test(t),tk=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tE={...tM,transform:t=>tk(0,1,t)},tV={...tM,default:1},tC=t=>Math.round(1e5*t)/1e5,tD=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tj=(t,e)=>i=>!!("string"==typeof i&&tR.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tL=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(tD);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tF=t=>tk(0,255,t),tB={...tM,transform:t=>Math.round(tF(t))},tO={test:tj("rgb","red"),parse:tL("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tB.transform(t)+", "+tB.transform(e)+", "+tB.transform(i)+", "+tC(tE.transform(r))+")"},tI={test:tj("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tO.transform},tz=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tU=tz("deg"),tN=tz("%"),t$=tz("px"),tW=tz("vh"),tY=tz("vw"),tH={...tN,parse:t=>tN.parse(t)/100,transform:t=>tN.transform(100*t)},tG={test:tj("hsl","hue"),parse:tL("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tN.transform(tC(e))+", "+tN.transform(tC(i))+", "+tC(tE.transform(r))+")"},tX={test:t=>tO.test(t)||tI.test(t)||tG.test(t),parse:t=>tO.test(t)?tO.parse(t):tG.test(t)?tG.parse(t):tI.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tO.transform(t):tG.transform(t)},tK=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tq="number",t_="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tJ(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tZ,t=>(tX.test(t)?(r.color.push(s),n.push(t_),i.push(tX.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tq),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tQ(t){return tJ(t).values}function t0(t){let{split:e,types:i}=tJ(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tq?n+=tC(t[s]):e===t_?n+=tX.transform(t[s]):n+=t[s]}return n}}let t1=t=>"number"==typeof t?0:t,t5={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tD))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tK))||void 0===i?void 0:i.length)||0)>0},parse:tQ,createTransformer:t0,getAnimatableNone:function(t){let e=tQ(t);return t0(t)(e.map(t1))}},t2=new Set(["brightness","contrast","saturate","opacity"]);function t3(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tD)||[];if(!r)return t;let n=i.replace(r,""),s=t2.has(e)?1:0;return r!==i&&(s*=100),e+"("+s+n+")"}let t9=/\b([a-z-]*)\(.*?\)/gu,t6={...t5,getAnimatableNone:t=>{let e=t.match(t9);return e?e.map(t3).join(" "):t}},t4={...tM,transform:Math.round},t8={borderWidth:t$,borderTopWidth:t$,borderRightWidth:t$,borderBottomWidth:t$,borderLeftWidth:t$,borderRadius:t$,radius:t$,borderTopLeftRadius:t$,borderTopRightRadius:t$,borderBottomRightRadius:t$,borderBottomLeftRadius:t$,width:t$,maxWidth:t$,height:t$,maxHeight:t$,top:t$,right:t$,bottom:t$,left:t$,padding:t$,paddingTop:t$,paddingRight:t$,paddingBottom:t$,paddingLeft:t$,margin:t$,marginTop:t$,marginRight:t$,marginBottom:t$,marginLeft:t$,backgroundPositionX:t$,backgroundPositionY:t$,rotate:tU,rotateX:tU,rotateY:tU,rotateZ:tU,scale:tV,scaleX:tV,scaleY:tV,scaleZ:tV,skew:tU,skewX:tU,skewY:tU,distance:t$,translateX:t$,translateY:t$,translateZ:t$,x:t$,y:t$,z:t$,perspective:t$,transformPerspective:t$,opacity:tE,originX:tH,originY:tH,originZ:t$,zIndex:t4,size:t$,fillOpacity:tE,strokeOpacity:tE,numOctaves:t4},t7={...t8,color:tX,backgroundColor:tX,outlineColor:tX,fill:tX,stroke:tX,borderColor:tX,borderTopColor:tX,borderRightColor:tX,borderBottomColor:tX,borderLeftColor:tX,filter:t6,WebkitFilter:t6},et=t=>t7[t];function ee(t,e){let i=et(t);return i!==t6&&(i=t5),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),er=t=>t===tM||t===t$,en=(t,e)=>parseFloat(t.split(", ")[e]),es=(t,e)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/u);if(n)return en(n[1],e);{let e=r.match(/^matrix\((.+)\)$/u);return e?en(e[1],t):0}},eo=new Set(["x","y","z"]),ea=$.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:es(4,13),y:es(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ed=!1;function ec(){if(ed){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var r;null===(r=t.getValue(e))||void 0===r||r.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ed=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ep(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ed=!0)})}class em{constructor(t,e,i,r,n,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,_.read(ep),_.resolveKeyframes(ec))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;for(let n=0;n<t.length;n++)if(null===t[n]){if(0===n){let n=null==r?void 0:r.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}else t[n]=t[n-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ef=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),eg=t=>e=>"string"==typeof e&&e.startsWith(t),ev=eg("--"),ey=eg("var(--"),eb=t=>!!ey(t)&&ex.test(t.split("/*")[0].trim()),ex=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ew=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eP=t=>e=>e.test(t),eT=[tM,t$,tN,tU,tY,tW,{test:t=>"auto"===t,parse:t=>t}],eS=t=>eT.find(eP(t));class eA extends em{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&eb(r=r.trim())){let n=function t(e,i,r=1){N(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=ew.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${null!=i?i:r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ef(t)?parseFloat(t):t}return eb(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!Y.has(i)||2!==t.length)return;let[r,n]=t,s=eS(r),o=eS(n);if(s!==o){if(er(s)&&er(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tA(r))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!ei.has(e)&&tJ(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=ee(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,o=r[s];r[s]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let ek=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t5.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function eE(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(eM),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return s&&void 0!==r?r:n[s]}class eV{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ep(),ec()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:n,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=ek(n,e),a=ek(s,e);return N(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||b(i))&&r)}(t,i,r,n)){if(tp.current||!s){a&&a(eE(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eC=(t,e,i)=>t+(e-t)*i;function eD(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eR(t,e){return i=>i>0?e:t}let ej=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},eL=[tI,tO,tG],eF=t=>eL.find(e=>e.test(t));function eB(t){let e=eF(t);if(N(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tG&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=eD(a,r,t+1/3),s=eD(a,r,t),o=eD(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let eO=(t,e)=>{let i=eB(t),r=eB(e);if(!i||!r)return eR(t,e);let n={...i};return t=>(n.red=ej(i.red,r.red,t),n.green=ej(i.green,r.green,t),n.blue=ej(i.blue,r.blue,t),n.alpha=eC(i.alpha,r.alpha,t),tO.transform(n))},eI=(t,e)=>i=>e(t(i)),ez=(...t)=>t.reduce(eI),eU=new Set(["none","hidden"]);function eN(t,e){return i=>eC(t,e,i)}function e$(t){return"number"==typeof t?eN:"string"==typeof t?eb(t)?eR:tX.test(t)?eO:eH:Array.isArray(t)?eW:"object"==typeof t?tX.test(t)?eO:eY:eR}function eW(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>e$(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function eY(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=e$(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let eH=(t,e)=>{let i=t5.createTransformer(e),r=tJ(t),n=tJ(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?eU.has(t)&&!n.values.length||eU.has(e)&&!r.values.length?eU.has(t)?i=>i<=0?t:e:i=>i>=1?e:t:ez(eW(function(t,e){var i;let r=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let o=e.types[s],a=t.indexes[o][n[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;r[s]=l,n[o]++}return r}(r,n),n.values),i):(N(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(t,e))};function eG(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eC(t,e,i):e$(t)(t,e)}function eX(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let eK={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eq(t,e){return t*Math.sqrt(1-e*e)}let e_=["duration","bounce"],eZ=["stiffness","damping","mass"];function eJ(t,e){return e.some(e=>void 0!==t[e])}function eQ(t=eK.visualDuration,e=eK.bounce){let i;let r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eK.velocity,stiffness:eK.stiffness,damping:eK.damping,mass:eK.mass,isResolvedFromDuration:!1,...t};if(!eJ(t,eZ)&&eJ(t,e_)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*tk(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:eK.mass,stiffness:r,damping:n}}else{let i=function({duration:t=eK.duration,bounce:e=eK.bounce,velocity:i=eK.velocity,mass:r=eK.mass}){let n,s;N(t<=z(eK.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tk(eK.minDamping,eK.maxDamping,o),t=tk(eK.minDuration,eK.maxDuration,U(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/eq(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=eq(Math.pow(e,2),o);return(r*i+i-s)*Math.exp(-r)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=z(t),isNaN(a))return{stiffness:eK.stiffness,damping:eK.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:eK.mass}).isResolvedFromDuration=!0}}return e}({...r,velocity:-U(r.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),v=a-o,b=U(Math.sqrt(u/d)),x=5>Math.abs(v);if(n||(n=x?eK.restSpeed.granular:eK.restSpeed.default),s||(s=x?eK.restDelta.granular:eK.restDelta.default),g<1){let t=eq(b,g);i=e=>a-Math.exp(-g*b*e)*((f+g*b*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-b*t)*(v+(f+b*v)*t);else{let t=b*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*b*e),r=Math.min(t*e,300);return a-i*((f+g*b*v)*Math.sinh(r)+t*v*Math.cosh(r))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let r=0;g<1&&(r=0===t?z(f):eX(i,t,e));let o=Math.abs(r)<=n,u=Math.abs(a-e)<=s;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(w),2e4),e=A(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,b=void 0===o?y:o(y);b!==y&&(v=b-p);let x=t=>-v*Math.exp(-t/r),w=t=>b+x(t),P=t=>{let e=x(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},T=t=>{f(m.value)&&(d=t,c=eQ({keyframes:[m.value,g(m.value)],velocity:eX(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,P(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||P(t),m)}}}let e1=tf(.42,0,1,1),e5=tf(0,0,.58,1),e2=tf(.42,0,.58,1),e3=t=>Array.isArray(t)&&"number"!=typeof t[0],e9={linear:N,easeIn:e1,easeInOut:e2,easeOut:e5,circIn:tP,circInOut:tS,circOut:tT,backIn:tb,backInOut:tx,backOut:ty,anticipate:tw},e6=t=>{if(w(t)){N(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return tf(e,i,r,n)}return"string"==typeof t?(N(void 0!==e9[t],`Invalid easing type '${t}'`),e9[t]):t};function e4({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){let n=e3(r)?r.map(e6):e6(r),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(N(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||eG,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=ez(Array.isArray(e)?e[i]||N:e,s)),r.push(s)}return r}(e,r,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=S(t[r],t[r+1],i);return a[r](n)};return i?e=>u(tk(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=S(0,e,r);t.push(eC(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||e2).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let e8=t=>{let e=({timestamp:e})=>t(e);return{start:()=>_.update(e,!0),stop:()=>Z(e),now:()=>J.isProcessing?J.timestamp:te.now()}},e7={decay:e0,inertia:e0,tween:e4,keyframes:e4,spring:eQ},it=t=>t/100;class ie extends eV{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:r,keyframes:n}=this.options,s=(null==r?void 0:r.KeyframeResolver)||em;this.resolver=new s(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:r="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=b(r)?r:e7[r]||e4;l!==e4&&"number"!=typeof t[0]&&(e=ez(it,eG(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:r,generator:n,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-d*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,b=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(b=s)),y=tk(0,1,i)*h}let x=v?{done:!1,value:a[0]}:b.next(y);o&&(x.value=o(x.value));let{done:w}=x;v||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&void 0!==r&&(x.value=eE(a,this.options,r)),f&&f(x.value),P&&this.finish(),x}get duration(){let{resolved:t}=this;return t?U(t.calculatedDuration):0}get time(){return U(this.currentTime)}set time(t){t=z(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e8,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:tw,backInOut:tx,circInOut:tS};class io extends eV{constructor(t){super(t);let{name:e,motionValue:i,element:r,keyframes:n}=this.options;this.resolver=new eA(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:r=300,times:n,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&T()&&s in is&&(s=is[s]),b((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&T()||!e||"string"==typeof e&&(e in M||T())||w(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:t[0]},n=[],s=0;for(;!r.done&&s<2e4;)n.push((r=i.sample(s)).value),s+=10;return{times:void 0,keyframes:n,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),r=h.duration,n=h.times,s=h.ease,o="keyframes"}let h=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&T()?A(e,i):w(e)?k(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:r,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:r,times:n,ease:s});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(x(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eE(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:n,type:o,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return U(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return U(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=z(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return N;let{animation:i}=e;x(i,t)}else this.pendingTimeline=t;return N}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:r,type:n,ease:s,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:r,type:n,ease:s,times:o,isGenerator:!0}),d=z(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ir()&&i&&ii.has(i)&&!a&&!l&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,{keyframes:e})=>e.length>2?iu:W.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,ic=(t,e,i,r={},n,s)=>o=>{let a=v(r,t)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=z(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...id(t,h)}),h.duration&&(h.duration=z(h.duration)),h.repeatDelay&&(h.repeatDelay=z(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(d=!0)),(tp.current||X.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!s&&void 0!==e.get()){let t=eE(h.keyframes,a);if(void 0!==t)return _.update(()=>{h.onUpdate(t),h.onComplete()}),new g([])}return!s&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:r,type:n}={}){var s;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;r&&(o=r);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let r=t.getValue(e,null!==(s=t.latestValues[e])&&void 0!==s?s:null),n=l[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(d,e))continue;let a={delay:i,...v(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tc];if(i){let t=window.MotionHandoffAnimation(i,e,_);null!==t&&(a.startTime=t,h=!0)}}th(t,e),r.start(ic(e,r,n,t.shouldReduceMotion&&Y.has(e)?{type:!1}:a,t,h));let c=r.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{_.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=h(t,e)||{};for(let e in n={...n,...i}){let i=G(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var r;let n=h(t,e,"exit"===i.type?null===(r=t.presenceContext)||void 0===r?void 0:r.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let o=n?()=>Promise.all(ip(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(ig).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+r,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function ig(t,e){return t.sortNodePosition(e)}let iv=c.length,iy=[...d].reverse(),ib=d.length;function ix(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iw(){return{animate:ix(!0),whileInView:ix(),whileHover:ix(),whileTap:ix(),whileDrag:ix(),whileFocus:ix(),exit:ix()}}class iP{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iT extends iP{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)r=im(t,e,i);else{let n="function"==typeof e?h(t,e,i.custom):e;r=Promise.all(ip(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iw(),r=!0,l=e=>(i,r)=>{var n;let s=h(t,r,"exit"===e?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iv;t++){let r=c[t],n=e.props[r];(a(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},p=[],m=new Set,f={},g=1/0;for(let e=0;e<ib;e++){var v;let c=iy[e],y=i[c],b=void 0!==h[c]?h[c]:d[c],x=a(b),w=c===u?y.isActive:null;!1===w&&(g=e);let P=b===d[c]&&b!==h[c]&&x;if(P&&r&&t.manuallyAnimateOnMount&&(P=!1),y.protectedKeys={...f},!y.isActive&&null===w||!b&&!y.prevProp||n(b)||"boolean"==typeof b)continue;let T=(v=y.prevProp,"string"==typeof b?b!==v:!!Array.isArray(b)&&!o(b,v)),S=T||c===u&&y.isActive&&!P&&x||e>g&&x,A=!1,k=Array.isArray(b)?b:[b],M=k.reduce(l(c),{});!1===w&&(M={});let{prevResolvedValues:E={}}=y,V={...E,...M},C=e=>{S=!0,m.has(e)&&(A=!0,m.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in V){let e=M[t],i=E[t];if(!f.hasOwnProperty(t))(s(e)&&s(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?C(t):y.protectedKeys[t]=!0:null!=e?C(t):m.add(t)}y.prevProp=b,y.prevResolvedValues=M,y.isActive&&(f={...f,...M}),r&&t.blockInitialAnimation&&(S=!1);let D=!(P&&T)||A;S&&D&&p.push(...k.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=r?r:null}),p.push({animation:e})}let y=!!p.length;return r&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(y=!1),r=!1,y?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,r){var n;if(i[e].isActive===r)return Promise.resolve();null===(n=t.variantChildren)||void 0===n||n.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,r)}),i[e].isActive=r;let s=u(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iw(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iS=0;class iA extends iP{constructor(){super(...arguments),this.id=iS++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function ik(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let iE=t=>e=>R(e)&&t(e,iM(e));function iV(t,e,i,r){return ik(t,e,iE(i),r)}let iC=(t,e)=>Math.abs(t-e);class iD{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=iL(this.lastMoveEventInfo,this.history),r=null!==this.startEvent,n=(t=i.offset,e={x:0,y:0},Math.sqrt(iC(t.x,e.x)**2+iC(t.y,e.y)**2)>=3);if(!r&&!n)return;let{point:s}=i,{timestamp:o}=J;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;r||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iR(e,this.transformPagePoint),_.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iL("pointercancel"===t.type?this.lastMoveEventInfo:iR(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!R(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iR(iM(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=J;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iL(s,this.history)),this.removeListeners=ez(iV(this.contextWindow,"pointermove",this.handlePointerMove),iV(this.contextWindow,"pointerup",this.handlePointerUp),iV(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function iR(t,e){return e?{point:e(t.point)}:t}function ij(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iL({point:t},e){return{point:t,delta:ij(t,iF(e)),offset:ij(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iF(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>z(.1)));)i--;if(!r)return{x:0,y:0};let s=U(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function iF(t){return t[t.length-1]}function iB(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iO(t){return t.max-t.min}function iI(t,e,i,r=.5){t.origin=r,t.originPoint=eC(e.min,e.max,t.origin),t.scale=iO(i)/iO(e),t.translate=eC(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iz(t,e,i,r){iI(t.x,e.x,i.x,r?r.originX:void 0),iI(t.y,e.y,i.y,r?r.originY:void 0)}function iU(t,e,i){t.min=i.min+e.min,t.max=t.min+iO(e)}function iN(t,e,i){t.min=e.min-i.min,t.max=t.min+iO(e)}function i$(t,e,i){iN(t.x,e.x,i.x),iN(t.y,e.y,i.y)}function iW(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iY(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function iH(t,e,i){return{min:iG(t,e),max:iG(t,i)}}function iG(t,e){return"number"==typeof t?t:t[e]||0}let iX=()=>({translate:0,scale:1,origin:0,originPoint:0}),iK=()=>({x:iX(),y:iX()}),iq=()=>({min:0,max:0}),i_=()=>({x:iq(),y:iq()});function iZ(t){return[t("x"),t("y")]}function iJ({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iQ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iQ(t)||!iQ(e)||!iQ(i)}function i1(t){return i0(t)||i5(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i5(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i2(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function i3(t,e=0,i=1,r,n){t.min=i2(t.min,e,i,r,n),t.max=i2(t.max,e,i,r,n)}function i9(t,{x:e,y:i}){i3(t.x,e.translate,e.scale,e.originPoint),i3(t.y,i.translate,i.scale,i.originPoint)}function i6(t,e){t.min=t.min+e,t.max=t.max+e}function i4(t,e,i,r,n=.5){let s=eC(t.min,t.max,n);i3(t,e,i,s,r)}function i8(t,e){i4(t.x,e.x,e.scaleX,e.scale,e.originX),i4(t.y,e.y,e.scaleY,e.scale,e.originY)}function i7(t,e){return iJ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let rt=({current:t})=>t?t.ownerDocument.defaultView:null,re=new WeakMap;class ri{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=i_(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iD(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?E[i]?null:(E[i]=!0,()=>{E[i]=!1}):E.x||E.y?null:(E.x=E.y=!0,()=>{E.x=E.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iZ(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];if(r){let t=iO(r);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&_.postRender(()=>n(t,e)),th(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iZ(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:rt(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&_.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rr(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?eC(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?eC(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&iB(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:iW(t.x,i,n),y:iW(t.y,e,r)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iH(t,"left","right"),y:iH(t,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&iZ(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iB(e))return!1;let r=e.current;N(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=i7(t,i),{scroll:n}=e;return n&&(i6(r.x,n.offset.x),i6(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o={x:iY((t=n.layout.layoutBox).x,s.x),y:iY(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iJ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iZ(o=>{if(!rr(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(ic(t,i,0,e,this.visualElement,!1))}stopAnimation(){iZ(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iZ(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iZ(e=>{let{drag:i}=this.getProps();if(!rr(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-eC(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iB(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iZ(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iO(t),n=iO(e);return n>r?i=S(e.min,e.max-r,t.min):r>n&&(i=S(t.min,t.max-n,e.min)),tk(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iZ(e=>{if(!rr(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(eC(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;re.set(this.visualElement,this);let t=iV(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iB(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),_.read(e);let n=ik(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iZ(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rr(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rn extends iP{constructor(t){super(t),this.removeGroupControls=N,this.removeListeners=N,this.controls=new ri(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||N}unmount(){this.removeGroupControls(),this.removeListeners()}}let rs=t=>(e,i)=>{t&&_.postRender(()=>t(e,i))};class ro extends iP{constructor(){super(...arguments),this.removePointerDownListener=N}onPointerDown(t){this.session=new iD(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rt(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rs(t),onStart:rs(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&_.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=iV(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ra,rl,ru,rh=i(7437),rd=i(2265),rc=i(9637),rp=i(8881);let rm=(0,rd.createContext)({}),rf={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rg(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let rv={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!t$.test(t))return t;t=parseFloat(t)}let i=rg(t,e.target.x),r=rg(t,e.target.y);return`${i}% ${r}%`}},ry={},{schedule:rb,cancel:rx}=q(queueMicrotask,!1);class rw extends rd.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;Object.assign(ry,rT),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rf.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,s=i.projection;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||_.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rb.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rP(t){let[e,i]=(0,rc.oO)(),r=(0,rd.useContext)(rp.p);return(0,rh.jsx)(rw,{...t,layoutGroup:r,switchLayoutGroup:(0,rd.useContext)(rm),isPresent:e,safeToRemove:i})}let rT={borderRadius:{...rv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rv,borderTopRightRadius:rv,borderBottomLeftRadius:rv,borderBottomRightRadius:rv,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=t5.parse(t);if(r.length>5)return t;let n=t5.createTransformer(t),s="number"!=typeof r[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=eC(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}},rS=(t,e)=>t.depth-e.depth;class rA{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){tr(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rS),this.isDirty=!1,this.children.forEach(t)}}function rk(t){let e=tu(t)?t.get():t;return H(e)?e.toValue():e}let rM=["TopLeft","TopRight","BottomLeft","BottomRight"],rE=rM.length,rV=t=>"string"==typeof t?parseFloat(t):t,rC=t=>"number"==typeof t||t$.test(t);function rD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rR=rL(0,.5,tT),rj=rL(.5,.95,N);function rL(t,e,i){return r=>r<t?0:r>e?1:i(S(t,e,r))}function rF(t,e){t.min=e.min,t.max=e.max}function rB(t,e){rF(t.x,e.x),rF(t.y,e.y)}function rO(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rI(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rz(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(tN.test(e)&&(e=parseFloat(e),e=eC(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eC(s.min,s.max,r);t===s&&(a-=e),t.min=rI(t.min,e,i,a,n),t.max=rI(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rU=["x","scaleX","originX"],rN=["y","scaleY","originY"];function r$(t,e,i,r){rz(t.x,e,rU,i?i.x:void 0,r?r.x:void 0),rz(t.y,e,rN,i?i.y:void 0,r?r.y:void 0)}function rW(t){return 0===t.translate&&1===t.scale}function rY(t){return rW(t.x)&&rW(t.y)}function rH(t,e){return t.min===e.min&&t.max===e.max}function rG(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rX(t,e){return rG(t.x,e.x)&&rG(t.y,e.y)}function rK(t){return iO(t.x)/iO(t.y)}function rq(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class r_{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(tr(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rZ={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rJ="undefined"!=typeof window&&void 0!==window.MotionDebug,rQ=["","X","Y","Z"],r0={visibility:"hidden"},r1=0;function r5(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function r2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=r1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rJ&&(rZ.totalNodes=rZ.resolvedTargetDeltas=rZ.recalculatedProjection=0),this.nodes.forEach(r6),this.nodes.forEach(nr),this.nodes.forEach(nn),this.nodes.forEach(r4),rJ&&window.MotionDebug.record(rZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tn),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:r,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||r)&&(this.isLayoutDirty=!0),t){let i;let r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),r=({timestamp:e})=>{let n=e-i;n>=250&&(Z(r),t(n-250))};return _.read(r,!0),()=>Z(r)}(r,0),rf.hasAnimatedSinceResize&&(rf.hasAnimatedSinceResize=!1,this.nodes.forEach(ni))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&s&&(r||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nh,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!rX(this.targetLayout,r)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...v(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ni(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ns),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[tc];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",_,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r7);return}this.isUpdating||this.nodes.forEach(nt),this.isUpdating=!1,this.nodes.forEach(ne),this.nodes.forEach(r3),this.nodes.forEach(r9),this.clearAllSnapshots();let t=te.now();J.delta=tk(0,1e3/60,t-J.timestamp),J.timestamp=t,J.isProcessing=!0,Q.update.process(J),Q.preRender.process(J),Q.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rb.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r8),this.sharedNodes.forEach(no)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,_.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){_.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=i_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rY(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),np((e=r).x),np(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return i_();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(nf))){let{scroll:t}=this.root;t&&(i6(i.x,t.offset.x),i6(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=i_();if(rB(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let r=this.path[e],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rB(i,t),i6(i.x,n.offset.x),i6(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=i_();rB(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i8(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i1(r.latestValues)&&i8(i,r.latestValues)}return i1(this.latestValues)&&i8(i,this.latestValues),i}removeTransform(t){let e=i_();rB(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let r=i_();rB(r,i.measurePageBox()),r$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return i1(this.latestValues)&&r$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,r,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),i$(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=i_(),this.targetWithTransforms=i_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,n=this.relativeParent.target,iU(i.x,r.x,n.x),iU(i.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rB(this.target,this.layout.layoutBox),i9(this.target,this.targetDelta)):rB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),i$(this.relativeTargetOrigin,this.target,t.target),rB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rJ&&rZ.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||i0(this.parent.latestValues)||i5(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===J.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;rB(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,r=!1){let n,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i8(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,i9(t,s)),r&&i1(n.latestValues)&&i8(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=i_());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rO(this.prevProjectionDelta.x,this.projectionDelta.x),rO(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iz(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rq(this.projectionDelta.x,this.prevProjectionDelta.x)&&rq(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rJ&&rZ.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iK(),this.projectionDelta=iK(),this.projectionDeltaWithTransform=iK()}setAnimationOrigin(t,e=!1){let i;let r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=iK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=i_(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nu));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(na(o.x,t.x,r),na(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m;i$(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nl(p.x,m.x,a.x,r),nl(p.y,m.y,a.y,r),i&&(u=this.relativeTarget,c=i,rH(u.x,c.x)&&rH(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=i_()),rB(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=eC(0,void 0!==i.opacity?i.opacity:1,rR(r)),t.opacityExit=eC(void 0!==e.opacity?e.opacity:1,0,rj(r))):s&&(t.opacity=eC(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let n=0;n<rE;n++){let s=`border${rM[n]}Radius`,o=rD(e,s),a=rD(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rC(o)===rC(a)?(t[s]=Math.max(eC(rV(o),rV(a),r),0),(tN.test(a)||tN.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=eC(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=_.update(()=>{rf.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let r=tu(0)?0:tl(0);return r.start(ic("",r,1e3,i)),r.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&nm(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||i_();let e=iO(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iO(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rB(e,i),i8(e,n),iz(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new r_),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&r5("z",t,r,this.animationValues);for(let e=0;e<rQ.length;e++)r5(`rotate${rQ[e]}`,t,r,this.animationValues),r5(`skew${rQ[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return r0;let r={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rk(null==t?void 0:t.pointerEvents)||"",r.transform=n?n(this.latestValues,""):"none",r;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rk(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),r.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(r.transform=n(o,r.transform));let{x:a,y:l}=this.projectionDelta;for(let t in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?r.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ry){if(void 0===o[t])continue;let{correct:e,applyTo:i}=ry[t],n="none"===r.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)r[i[e]]=n}else r[t]=n}return this.options.layoutId&&(r.pointerEvents=s===this?rk(null==t?void 0:t.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(r7),this.root.sharedNodes.clear()}}}function r3(t){t.updateLayout()}function r9(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:r}=t.layout,{animationType:n}=t.options,s=i.source!==t.layout.source;"size"===n?iZ(t=>{let r=s?i.measuredBox[t]:i.layoutBox[t],n=iO(r);r.min=e[t].min,r.max=r.min+n}):nm(n,i.layoutBox,e)&&iZ(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],o=iO(e[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iK();iz(o,e,i.layoutBox);let a=iK();s?iz(a,t.applyTransform(r,!0),i.measuredBox):iz(a,e,i.layoutBox);let l=!rY(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=i_();i$(o,i.layoutBox,n.layoutBox);let a=i_();i$(a,e,s.layoutBox),rX(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r6(t){rJ&&rZ.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function r4(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function r8(t){t.clearSnapshot()}function r7(t){t.clearMeasurements()}function nt(t){t.isLayoutDirty=!1}function ne(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ni(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nr(t){t.resolveTargetDelta()}function nn(t){t.calcProjection()}function ns(t){t.resetSkewAndRotation()}function no(t){t.removeLeadSnapshot()}function na(t,e,i){t.translate=eC(e.translate,0,i),t.scale=eC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nl(t,e,i,r){t.min=eC(e.min,i.min,r),t.max=eC(e.max,i.max,r)}function nu(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nh={duration:.45,ease:[.4,0,.1,1]},nd=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nc=nd("applewebkit/")&&!nd("chrome/")?Math.round:N;function np(t){t.min=nc(t.min),t.max=nc(t.max)}function nm(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rK(e)-rK(i)))}function nf(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let ng=r2({attachResizeListener:(t,e)=>ik(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},ny=r2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nv.current){let t=new ng({});t.mount(window),t.setOptions({layoutScroll:!0}),nv.current=t}return nv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nb(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&_.postRender(()=>n(e,iM(e)))}class nx extends iP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=V(t,i),o=C(t=>{let{target:i}=t,r=e(t);if("function"!=typeof r||!i)return;let s=C(t=>{r(t),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,n)});return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,t=>(nb(this.node,t,"Start"),t=>nb(this.node,t,"End"))))}unmount(){}}class nw extends iP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ez(ik(this.node.current,"focus",()=>this.onFocus()),ik(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nP(t,e,i){let{props:r}=t;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&_.postRender(()=>n(e,iM(e)))}class nT extends iP{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=V(t,i),o=t=>{let r=t.currentTarget;if(!I(t)||L.has(r))return;L.add(r);let s=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(t)&&L.has(r)&&(L.delete(r),"function"==typeof s&&s(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||D(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{j.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>O(t,n),n)}),s}(t,t=>(nP(this.node,t,"Start"),(t,{success:e})=>nP(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nS=new WeakMap,nA=new WeakMap,nk=t=>{let e=nS.get(t.target);e&&e(t)},nM=t=>{t.forEach(nk)},nE={some:0,all:1};class nV extends iP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nE[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nA.has(i)||nA.set(i,{});let r=nA.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nM,{root:t,...e})),r[n]}(e);return nS.set(t,i),r.observe(t),()=>{nS.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nC=(0,rd.createContext)({strict:!1});var nD=i(5750);let nR=(0,rd.createContext)({});function nj(t){return n(t.animate)||c.some(e=>a(t[e]))}function nL(t){return!!(nj(t)||t.variants)}function nF(t){return Array.isArray(t)?t.join(" "):t}var nB=i(4563);let nO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nI={};for(let t in nO)nI[t]={isEnabled:e=>nO[t].some(t=>!!e[t])};let nz=Symbol.for("motionComponentSymbol");var nU=i(4252),nN=i(1534);let n$=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nW(t){if("string"!=typeof t||t.includes("-"));else if(n$.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nY=i(3576);let nH=t=>(e,i)=>{let r=(0,rd.useContext)(nR),s=(0,rd.useContext)(nU.O),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},r,s,o){let a={latestValues:function(t,e,i,r){let s={},o=r(t,{});for(let t in o)s[t]=rk(o[t]);let{initial:a,animate:l}=t,h=nj(t),d=nL(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=u(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(r,s,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:r,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,r,s);return i?o():(0,nY.h)(o)},nG=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nX={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nK=$.length;function nq(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(W.has(t)){o=!0;continue}if(ev(t)){n[t]=i;continue}{let e=nG(i,t8[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nK;s++){let o=$[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=nG(a,t8[o]);if(!l){n=!1;let e=nX[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let n_={offset:"stroke-dashoffset",array:"stroke-dasharray"},nZ={offset:"strokeDashoffset",array:"strokeDasharray"};function nJ(t,e,i){return"string"==typeof t?t:t$.transform(e+i*t)}function nQ(t,{attrX:e,attrY:i,attrScale:r,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(nq(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==s||p.transform)&&(p.transformOrigin=function(t,e,i){let r=nJ(e,t.x,t.width),n=nJ(i,t.y,t.height);return`${r} ${n}`}(m,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==r&&(c.scale=r),void 0!==o&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?n_:nZ;t[s.offset]=t$.transform(-r);let o=t$.transform(e),a=t$.transform(i);t[s.array]=`${o} ${a}`}(c,o,a,l,!1)}let n0=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),n1=()=>({...n0(),attrs:{}}),n5=t=>"string"==typeof t&&"svg"===t.toLowerCase();function n2(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}let n3=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function n9(t,e,i,r){for(let i in n2(t,e,void 0,r),e.attrs)t.setAttribute(n3.has(i)?i:td(i),e.attrs[i])}function n6(t,{layout:e,layoutId:i}){return W.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ry[t]||"opacity"===t)}function n4(t,e,i){var r;let{style:n}=t,s={};for(let o in n)(tu(n[o])||e.style&&tu(e.style[o])||n6(o,t)||(null===(r=null==i?void 0:i.getValue(o))||void 0===r?void 0:r.liveStyle)!==void 0)&&(s[o]=n[o]);return s}function n8(t,e,i){let r=n4(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(r[-1!==$.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n7=["x","y","width","height","cx","cy","r"],st={useVisualState:nH({scrapeMotionValuesFromProps:n8,createRenderState:n1,onUpdate:({props:t,prevProps:e,current:i,renderState:r,latestValues:n})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in n)if(W.has(t)){s=!0;break}}if(!s)return;let o=!e;if(e)for(let i=0;i<n7.length;i++){let r=n7[i];t[r]!==e[r]&&(o=!0)}o&&_.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,r),_.render(()=>{nQ(r,n,n5(i.tagName),t.transformTemplate),n9(i,r)})})}})},se={useVisualState:nH({scrapeMotionValuesFromProps:n4,createRenderState:n0})};function si(t,e,i){for(let r in e)tu(e[r])||n6(r,i)||(t[r]=e[r])}let sr=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function sn(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||sr.has(t)}let ss=t=>!sn(t);try{(ra=require("@emotion/is-prop-valid").default)&&(ss=t=>t.startsWith("on")?!sn(t):ra(t))}catch(t){}let so={current:null},sa={current:!1},sl=[...eT,tX,t5],su=t=>sl.find(eP(t)),sh=new WeakMap,sd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sc{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=em,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,_.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nj(e),this.isVariantNode=nL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sh.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sa.current||function(){if(sa.current=!0,nB.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>so.current=t.matches;t.addListener(e),e()}else so.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||so.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in sh.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=W.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&_.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nI){let e=nI[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):i_()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sd.length;e++){let i=sd[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(tu(n))t.addValue(r,n);else if(tu(s))t.addValue(r,tl(n,{owner:t}));else if(s!==n){if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,tl(void 0!==e?e:n,{owner:t}))}}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let r=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=r&&("string"==typeof r&&(ef(r)||tA(r))?r=parseFloat(r):!su(r)&&t5.test(e)&&(r=ee(t,e)),this.setBaseTarget(t,tu(r)?r.get():r)),tu(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=u(this.props,r,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);n&&(i=n[t])}if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tu(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tn),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sp extends sc{constructor(){super(...arguments),this.KeyframeResolver=eA}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sm extends sp{constructor(){super(...arguments),this.type="html",this.renderInstance=n2}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),r=(ev(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i7(t,e)}build(t,e,i){nq(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n4(t,e,i)}}class sf extends sp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=i_}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}return e=n3.has(e)?e:td(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n8(t,e,i)}build(t,e,i){nQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,r){n9(t,e,i,r)}mount(t){this.isSVGTag=n5(t.tagName),super.mount(t)}}let sg=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((rl={animation:{Feature:iT},exit:{Feature:iA},inView:{Feature:nV},tap:{Feature:nT},focus:{Feature:nw},hover:{Feature:nx},pan:{Feature:ro},drag:{Feature:rn,ProjectionNode:ny,MeasureLayout:rP},layout:{ProjectionNode:ny,MeasureLayout:rP}},ru=(t,e)=>nW(t)?new sf(e):new sm(e,{allowProjection:t!==rd.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:o,Component:l}=t;function u(t,e){var i;let r;let u={...(0,rd.useContext)(nD._),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,rd.useContext)(rp.p).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(nj(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rd.useContext)(nR));return(0,rd.useMemo)(()=>({initial:e,animate:i}),[nF(e),nF(i)])}(t),c=o(t,h);if(!h&&nB.j){(0,rd.useContext)(nC).strict;let t=function(t){let{drag:e,layout:i}=nI;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);r=t.MeasureLayout,d.visualElement=function(t,e,i,r,n){var s,o;let{visualElement:a}=(0,rd.useContext)(nR),l=(0,rd.useContext)(nC),u=(0,rd.useContext)(nU.O),h=(0,rd.useContext)(nD._).reducedMotion,d=(0,rd.useRef)(null);r=r||l.renderer,!d.current&&r&&(d.current=r(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,rd.useContext)(rm);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&iB(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,rd.useRef)(!1);(0,rd.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tc],g=(0,rd.useRef)(!!f&&!(null===(s=window.MotionHandoffIsComplete)||void 0===s?void 0:s.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return(0,nN.L)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),rb.render(c.render),g.current&&c.animationState&&c.animationState.animateChanges())}),(0,rd.useEffect)(()=>{c&&(!g.current&&c.animationState&&c.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),g.current=!1))}),c}(l,c,u,n,t.ProjectionNode)}return(0,rh.jsxs)(nR.Provider,{value:d,children:[r&&d.visualElement?(0,rh.jsx)(r,{visualElement:d.visualElement,...u}):null,s(l,t,(i=d.visualElement,(0,rd.useCallback)(t=>{t&&c.onMount&&c.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iB(e)&&(e.current=t))},[i])),c,h,d.visualElement)]})}r&&function(t){for(let e in t)nI[e]={...nI[e],...t[e]}}(r),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(i=null!==(e=l.displayName)&&void 0!==e?e:l.name)&&void 0!==i?i:"",")"));let h=(0,rd.forwardRef)(u);return h[nz]=l,h}({...nW(t)?st:se,preloadedFeatures:rl,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(nW(e)?function(t,e,i,r){let n=(0,rd.useMemo)(()=>{let i=n1();return nQ(i,e,n5(r),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};si(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return si(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rd.useMemo)(()=>{let i=n0();return nq(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(ss(n)||!0===i&&sn(n)||!e&&!sn(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==rd.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,rd.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,rd.createElement)(e,{...l,children:h})}}(e),createVisualElement:ru,Component:t})}))},4563:function(t,e,i){i.d(e,{j:function(){return r}});let r="undefined"!=typeof window},3576:function(t,e,i){i.d(e,{h:function(){return n}});var r=i(2265);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},1534:function(t,e,i){i.d(e,{L:function(){return n}});var r=i(2265);let n=i(4563).j?r.useLayoutEffect:r.useEffect},3335:function(t,e,i){i.d(e,{m6:function(){return td}});let r=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),n(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=i[t]||[];return e&&r[t]?[...n,...r[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),s=r?n(t.slice(1),r):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)l(i[t],r,t,e);return r},l=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t){if(h(t)){l(t(r),e,i,r);return}e.validators.push({validator:t,classGroupId:i});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),i,r)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}},c=t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e;let i=[],r=0,n=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===r&&0===n){if(":"===a){i.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?r++:"]"===a?r--:"("===a?n++:")"===a&&n--}let o=0===i.length?t:t.substring(s),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}},f=t=>({cache:d(t.cacheSize),parseClassName:c(t),sortModifiers:m(t),...r(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(h).join(":"),v=d?g+"!":g,y=v+f;if(o.includes(y))continue;o.push(y);let b=n(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(v+e)}l=e+(l.length>0?" "+l:l)}return l};function y(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=b(t))&&(r&&(r+=" "),r+=e);return r}let b=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=b(t[r]))&&(i&&(i+=" "),i+=e);return i},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,P=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=t=>T.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),R=t=>t.endsWith("%")&&C(t.slice(0,-1)),j=t=>S.test(t),L=()=>!0,F=t=>A.test(t)&&!k.test(t),B=()=>!1,O=t=>M.test(t),I=t=>E.test(t),z=t=>!N(t)&&!X(t),U=t=>tt(t,to,B),N=t=>w.test(t),$=t=>tt(t,ta,F),W=t=>tt(t,tl,C),Y=t=>tt(t,ti,B),H=t=>tt(t,tn,I),G=t=>tt(t,B,O),X=t=>P.test(t),K=t=>te(t,ta),q=t=>te(t,tu),_=t=>te(t,ti),Z=t=>te(t,to),J=t=>te(t,tn),Q=t=>te(t,th,!0),tt=(t,e,i)=>{let r=w.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},te=(t,e,i=!1)=>{let r=P.exec(t);return!!r&&(r[1]?e(r[1]):i)},ti=t=>"position"===t,tr=new Set(["image","url"]),tn=t=>tr.has(t),ts=new Set(["length","size","percentage"]),to=t=>ts.has(t),ta=t=>"length"===t,tl=t=>"number"===t,tu=t=>"family-name"===t,th=t=>"shadow"===t,td=function(t,...e){let i,r,n;let s=function(a){return r=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=i.cache.set,s=o,o(a)};function o(t){let e=r(t);if(e)return e;let s=v(t,i);return n(t,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let t=x("color"),e=x("font"),i=x("text"),r=x("font-weight"),n=x("tracking"),s=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),h=x("shadow"),d=x("inset-shadow"),c=x("drop-shadow"),p=x("blur"),m=x("perspective"),f=x("aspect"),g=x("ease"),v=x("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],w=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],T=()=>[V,"px","full","auto",X,N,l],S=()=>[D,"none","subgrid",X,N],A=()=>["auto",{span:["full",D,X,N]},X,N],k=()=>[D,"auto",X,N],M=()=>["auto","min","max","fr",X,N],E=()=>[X,N,l],F=()=>["start","end","center","between","around","evenly","stretch","baseline"],B=()=>["start","end","center","stretch"],O=()=>[X,N,l],I=()=>["px",...O()],tt=()=>["px","auto",...O()],te=()=>[V,"auto","px","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",X,N,l],ti=()=>[t,X,N],tr=()=>[R,$],tn=()=>["","none","full",u,X,N],ts=()=>["",C,K,$],to=()=>["solid","dashed","dotted","double"],ta=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tl=()=>["","none",p,X,N],tu=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X,N],th=()=>["none",C,X,N],td=()=>["none",C,X,N],tc=()=>[C,X,N],tp=()=>[V,"full","px",X,N,l];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[L],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:[C],text:[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,N,X,f]}],container:["container"],columns:[{columns:[C,N,X,a]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...b(),N,X]}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",X,N]}],basis:[{basis:[V,"full","auto",X,N,a,l]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,V,"auto","initial","none",N]}],grow:[{grow:["",C,X,N]}],shrink:[{shrink:["",C,X,N]}],order:[{order:[D,"first","last","none",X,N]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:E()}],"gap-x":[{"gap-x":E()}],"gap-y":[{"gap-y":E()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...B(),"normal"]}],"justify-self":[{"justify-self":["auto",...B()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...B(),"baseline"]}],"align-self":[{self:["auto",...B(),"baseline"]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...B(),"baseline"]}],"place-self":[{"place-self":["auto",...B()]}],p:[{p:I()}],px:[{px:I()}],py:[{py:I()}],ps:[{ps:I()}],pe:[{pe:I()}],pt:[{pt:I()}],pr:[{pr:I()}],pb:[{pb:I()}],pl:[{pl:I()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":O()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":O()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen",...te()]}],"min-h":[{"min-h":["screen","none",...te()]}],"max-h":[{"max-h":["screen",...te()]}],"font-size":[{text:["base",i,K,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,X,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,N]}],"font-family":[{font:[q,N,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,X,N]}],"line-clamp":[{"line-clamp":[C,"none",X,W]}],leading:[{leading:[X,N,s,l]}],"list-image":[{"list-image":["none",X,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...to(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",X,$]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[C,"auto",X,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:["px",...O()]}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...b(),_,Y]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Z,U]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,X,N],radial:["",X,N],conic:[D,X,N]},J,H]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:tr()}],"gradient-via-pos":[{via:tr()}],"gradient-to-pos":[{to:tr()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:tn()}],"rounded-s":[{"rounded-s":tn()}],"rounded-e":[{"rounded-e":tn()}],"rounded-t":[{"rounded-t":tn()}],"rounded-r":[{"rounded-r":tn()}],"rounded-b":[{"rounded-b":tn()}],"rounded-l":[{"rounded-l":tn()}],"rounded-ss":[{"rounded-ss":tn()}],"rounded-se":[{"rounded-se":tn()}],"rounded-ee":[{"rounded-ee":tn()}],"rounded-es":[{"rounded-es":tn()}],"rounded-tl":[{"rounded-tl":tn()}],"rounded-tr":[{"rounded-tr":tn()}],"rounded-br":[{"rounded-br":tn()}],"rounded-bl":[{"rounded-bl":tn()}],"border-w":[{border:ts()}],"border-w-x":[{"border-x":ts()}],"border-w-y":[{"border-y":ts()}],"border-w-s":[{"border-s":ts()}],"border-w-e":[{"border-e":ts()}],"border-w-t":[{"border-t":ts()}],"border-w-r":[{"border-r":ts()}],"border-w-b":[{"border-b":ts()}],"border-w-l":[{"border-l":ts()}],"divide-x":[{"divide-x":ts()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ts()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...to(),"hidden","none"]}],"divide-style":[{divide:[...to(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...to(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,X,N]}],"outline-w":[{outline:["",C,K,$]}],"outline-color":[{outline:[t]}],shadow:[{shadow:["","none",h,Q,G]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",X,N,d]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:ts()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[C,$]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":ts()}],"inset-ring-color":[{"inset-ring":ti()}],opacity:[{opacity:[C,X,N]}],"mix-blend":[{"mix-blend":[...ta(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ta()}],filter:[{filter:["","none",X,N]}],blur:[{blur:tl()}],brightness:[{brightness:[C,X,N]}],contrast:[{contrast:[C,X,N]}],"drop-shadow":[{"drop-shadow":["","none",c,X,N]}],grayscale:[{grayscale:["",C,X,N]}],"hue-rotate":[{"hue-rotate":[C,X,N]}],invert:[{invert:["",C,X,N]}],saturate:[{saturate:[C,X,N]}],sepia:[{sepia:["",C,X,N]}],"backdrop-filter":[{"backdrop-filter":["","none",X,N]}],"backdrop-blur":[{"backdrop-blur":tl()}],"backdrop-brightness":[{"backdrop-brightness":[C,X,N]}],"backdrop-contrast":[{"backdrop-contrast":[C,X,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,X,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,X,N]}],"backdrop-invert":[{"backdrop-invert":["",C,X,N]}],"backdrop-opacity":[{"backdrop-opacity":[C,X,N]}],"backdrop-saturate":[{"backdrop-saturate":[C,X,N]}],"backdrop-sepia":[{"backdrop-sepia":["",C,X,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":O()}],"border-spacing-x":[{"border-spacing-x":O()}],"border-spacing-y":[{"border-spacing-y":O()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",X,N]}],ease:[{ease:["linear","initial",g,X,N]}],delay:[{delay:[C,X,N]}],animate:[{animate:["none",v,X,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,X,N]}],"perspective-origin":[{"perspective-origin":tu()}],rotate:[{rotate:th()}],"rotate-x":[{"rotate-x":th()}],"rotate-y":[{"rotate-y":th()}],"rotate-z":[{"rotate-z":th()}],scale:[{scale:td()}],"scale-x":[{"scale-x":td()}],"scale-y":[{"scale-y":td()}],"scale-z":[{"scale-z":td()}],"scale-3d":["scale-3d"],skew:[{skew:tc()}],"skew-x":[{"skew-x":tc()}],"skew-y":[{"skew-y":tc()}],transform:[{transform:[X,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:tu()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tp()}],"translate-x":[{"translate-x":tp()}],"translate-y":[{"translate-y":tp()}],"translate-z":[{"translate-z":tp()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,N]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[C,K,$,W]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}})}}]);