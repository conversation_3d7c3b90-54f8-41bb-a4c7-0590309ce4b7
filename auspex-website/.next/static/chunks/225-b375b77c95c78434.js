"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[225],{8336:function(e,t,n){let r;n.d(t,{x8:function(){return ta},VY:function(){return tn},dk:function(){return to},aV:function(){return tt},h_:function(){return te},fC:function(){return e4},Dx:function(){return tr},xz:function(){return e9}});var o,a,i,c,u,l,s,d=n(2265),f=n(6741),p=n(8575),h=n(3966),v=n(9255),m=n(886),g=n(5278),y=n(2912),b=n(6606),E=n(7437),w="focusScope.autoFocusOnMount",x="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},R=d.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,u]=d.useState(null),l=(0,b.W)(o),s=(0,b.W)(a),f=d.useRef(null),h=(0,p.e)(t,e=>u(e)),v=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(r){let e=function(e){if(v.paused||!c)return;let t=e.target;c.contains(t)?f.current=t:k(f.current,{select:!0})},t=function(e){if(v.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||k(f.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&k(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,v.paused]),d.useEffect(()=>{if(c){j.add(v);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(w,C);c.addEventListener(w,l),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(k(r,{select:t}),document.activeElement!==n)return}(M(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&k(c))}return()=>{c.removeEventListener(w,l),setTimeout(()=>{let t=new CustomEvent(x,C);c.addEventListener(x,s),c.dispatchEvent(t),t.defaultPrevented||k(null!=e?e:document.body,{select:!0}),c.removeEventListener(x,s),j.remove(v)},0)}}},[c,l,s,v]);let m=d.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=M(e);return[S(t,e),S(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&k(a,{select:!0})):(e.preventDefault(),n&&k(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,E.jsx)(y.WV.div,{tabIndex:-1,...i,ref:h,onKeyDown:m})});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function S(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function k(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}R.displayName="FocusScope";var j=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=N(r,e)).unshift(e)},remove(e){var t;null===(t=(r=N(r,e))[0])||void 0===t||t.resume()}});function N(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var A=n(3832),D=n(1599),I=0;function P(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var O=function(){return(O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function L(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var T="right-scroll-bar-position",F="width-before-scroll-bar";function W(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var _="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,V=new WeakMap,B=(void 0===o&&(o={}),(void 0===a&&(a=function(e){return e}),i=[],c=!1,u={read:function(){if(c)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=a(e,c);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(c=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){c=!0;var t=[];if(i.length){var n=i;i=[],n.forEach(e),t=i}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),i={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),i}}}}).options=O({async:!0,ssr:!1},o),u),Z=function(){},z=d.forwardRef(function(e,t){var n,r,o,a,i=d.useRef(null),c=d.useState({onScrollCapture:Z,onWheelCapture:Z,onTouchMoveCapture:Z}),u=c[0],l=c[1],s=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,g=e.sideCar,y=e.noIsolation,b=e.inert,E=e.allowPinchZoom,w=e.as,x=e.gapMode,C=L(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[i,t],r=function(e){return n.forEach(function(t){return W(t,e)})},(o=(0,d.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,_(function(){var e=V.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||W(e,null)}),r.forEach(function(e){t.has(e)||W(e,o)})}V.set(a,n)},[n]),a),M=O(O({},C),u);return d.createElement(d.Fragment,null,v&&d.createElement(g,{sideCar:B,removeScrollBar:h,shards:m,noIsolation:y,inert:b,setCallbacks:l,allowPinchZoom:!!E,lockRef:i,gapMode:x}),s?d.cloneElement(d.Children.only(f),O(O({},M),{ref:R})):d.createElement(void 0===w?"div":w,O({},M,{className:p,ref:R}),f))});z.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},z.classNames={fullWidth:F,zeroRight:T};var K=function(e){var t=e.sideCar,n=L(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return d.createElement(r,O({},n))};K.isSideCarExport=!0;var X=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Y=function(){var e=X();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},H=function(){var e=Y();return function(t){return e(t.styles,t.dynamic),null}},q={left:0,top:0,right:0,gap:0},U=function(e){return parseInt(e||"",10)||0},G=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[U(n),U(r),U(o)]},J=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return q;var t=G(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Q=H(),$="data-scroll-locked",ee=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat($,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(T," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(F," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(T," .").concat(T," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(F," .").concat(F," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat($,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},et=function(){var e=parseInt(document.body.getAttribute($)||"0",10);return isFinite(e)?e:0},en=function(){d.useEffect(function(){return document.body.setAttribute($,(et()+1).toString()),function(){var e=et()-1;e<=0?document.body.removeAttribute($):document.body.setAttribute($,e.toString())}},[])},er=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;en();var a=d.useMemo(function(){return J(o)},[o]);return d.createElement(Q,{styles:ee(a,!t,o,n?"":"!important")})},eo=!1;if("undefined"!=typeof window)try{var ea=Object.defineProperty({},"passive",{get:function(){return eo=!0,!0}});window.addEventListener("test",ea,ea),window.removeEventListener("test",ea,ea)}catch(e){eo=!1}var ei=!!eo&&{passive:!1},ec=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},eu=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),el(e,r)){var o=es(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},el=function(e,t){return"v"===e?ec(t,"overflowY"):ec(t,"overflowX")},es=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ed=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,p=0;do{var h=es(e,u),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&el(e,u)&&(f+=m,p+=v),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-c>p)&&(s=!0),s},ef=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ep=function(e){return[e.deltaX,e.deltaY]},eh=function(e){return e&&"current"in e?e.current:e},ev=0,em=[],eg=(l=function(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(ev++)[0],a=d.useState(H)[0],i=d.useRef(e);d.useEffect(function(){i.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eh),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=ef(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=eu(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=eu(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var p=r.current||o;return ed(p,t,e,"h"===p?u:l,!0)},[]),u=d.useCallback(function(e){if(em.length&&em[em.length-1]===a){var n="deltaY"in e?ep(e):ef(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(eh).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=d.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),s=d.useCallback(function(e){n.current=ef(e),r.current=void 0},[]),f=d.useCallback(function(t){l(t.type,ep(t),t.target,c(t,e.lockRef.current))},[]),p=d.useCallback(function(t){l(t.type,ef(t),t.target,c(t,e.lockRef.current))},[]);d.useEffect(function(){return em.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,ei),document.addEventListener("touchmove",u,ei),document.addEventListener("touchstart",s,ei),function(){em=em.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,ei),document.removeEventListener("touchmove",u,ei),document.removeEventListener("touchstart",s,ei)}},[]);var h=e.removeScrollBar,v=e.inert;return d.createElement(d.Fragment,null,v?d.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?d.createElement(er,{gapMode:e.gapMode}):null)},B.useMedium(l),K),ey=d.forwardRef(function(e,t){return d.createElement(z,O({},e,{ref:t,sideCar:eg}))});ey.classNames=z.classNames;var eb=new WeakMap,eE=new WeakMap,ew={},ex=0,eC=function(e){return e&&(e.host||eC(e.parentNode))},eR=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eC(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});ew[n]||(ew[n]=new WeakMap);var a=ew[n],i=[],c=new Set,u=new Set(o),l=function(e){!e||c.has(e)||(c.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(c.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(eb.get(e)||0)+1,l=(a.get(e)||0)+1;eb.set(e,u),a.set(e,l),i.push(e),1===u&&o&&eE.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),c.clear(),ex++,function(){i.forEach(function(e){var t=eb.get(e)-1,o=a.get(e)-1;eb.set(e,t),a.set(e,o),t||(eE.has(e)||e.removeAttribute(r),eE.delete(e)),o||e.removeAttribute(n)}),--ex||(eb=new WeakMap,eb=new WeakMap,eE=new WeakMap,ew={})}},eM=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eR(r,o,n,"aria-hidden")):function(){return null}},eS=d.forwardRef((e,t)=>{let{children:n,...r}=e,o=d.Children.toArray(n),a=o.find(eN);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:d.Children.count(e)>1?d.Children.only(null):d.isValidElement(e)?e.props.children:null);return(0,E.jsx)(ek,{...r,ref:t,children:d.isValidElement(e)?d.cloneElement(e,void 0,n):null})}return(0,E.jsx)(ek,{...r,ref:t,children:n})});eS.displayName="Slot";var ek=d.forwardRef((e,t)=>{let{children:n,...r}=e;if(d.isValidElement(n)){let e,o;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,i=function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==d.Fragment&&(i.ref=t?(0,p.F)(t,a):a),d.cloneElement(n,i)}return d.Children.count(n)>1?d.Children.only(null):null});ek.displayName="SlotClone";var ej=({children:e})=>(0,E.jsx)(E.Fragment,{children:e});function eN(e){return d.isValidElement(e)&&e.type===ej}var eA="Dialog",[eD,eI]=(0,h.b)(eA),[eP,eO]=eD(eA),eL=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,c=d.useRef(null),u=d.useRef(null),[l=!1,s]=(0,m.T)({prop:r,defaultProp:o,onChange:a});return(0,E.jsx)(eP,{scope:t,triggerRef:c,contentRef:u,contentId:(0,v.M)(),titleId:(0,v.M)(),descriptionId:(0,v.M)(),open:l,onOpenChange:s,onOpenToggle:d.useCallback(()=>s(e=>!e),[s]),modal:i,children:n})};eL.displayName=eA;var eT="DialogTrigger",eF=d.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eO(eT,n),a=(0,p.e)(t,o.triggerRef);return(0,E.jsx)(y.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e2(o.open),...r,ref:a,onClick:(0,f.M)(e.onClick,o.onOpenToggle)})});eF.displayName=eT;var eW="DialogPortal",[e_,eV]=eD(eW,{forceMount:void 0}),eB=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=eO(eW,t);return(0,E.jsx)(e_,{scope:t,forceMount:n,children:d.Children.map(r,e=>(0,E.jsx)(D.z,{present:n||a.open,children:(0,E.jsx)(A.h,{asChild:!0,container:o,children:e})}))})};eB.displayName=eW;var eZ="DialogOverlay",ez=d.forwardRef((e,t)=>{let n=eV(eZ,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eO(eZ,e.__scopeDialog);return a.modal?(0,E.jsx)(D.z,{present:r||a.open,children:(0,E.jsx)(eK,{...o,ref:t})}):null});ez.displayName=eZ;var eK=d.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eO(eZ,n);return(0,E.jsx)(ey,{as:eS,allowPinchZoom:!0,shards:[o.contentRef],children:(0,E.jsx)(y.WV.div,{"data-state":e2(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eX="DialogContent",eY=d.forwardRef((e,t)=>{let n=eV(eX,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eO(eX,e.__scopeDialog);return(0,E.jsx)(D.z,{present:r||a.open,children:a.modal?(0,E.jsx)(eH,{...o,ref:t}):(0,E.jsx)(eq,{...o,ref:t})})});eY.displayName=eX;var eH=d.forwardRef((e,t)=>{let n=eO(eX,e.__scopeDialog),r=d.useRef(null),o=(0,p.e)(t,n.contentRef,r);return d.useEffect(()=>{let e=r.current;if(e)return eM(e)},[]),(0,E.jsx)(eU,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,f.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,f.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,f.M)(e.onFocusOutside,e=>e.preventDefault())})}),eq=d.forwardRef((e,t)=>{let n=eO(eX,e.__scopeDialog),r=d.useRef(!1),o=d.useRef(!1);return(0,E.jsx)(eU,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let c=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(c))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eU=d.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,c=eO(eX,n),u=d.useRef(null),l=(0,p.e)(t,u);return d.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:P()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:P()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),I--}},[]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(R,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,E.jsx)(g.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":e2(c.open),...i,ref:l,onDismiss:()=>c.onOpenChange(!1)})}),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(e3,{titleId:c.titleId}),(0,E.jsx)(e7,{contentRef:u,descriptionId:c.descriptionId})]})]})}),eG="DialogTitle",eJ=d.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eO(eG,n);return(0,E.jsx)(y.WV.h2,{id:o.titleId,...r,ref:t})});eJ.displayName=eG;var eQ="DialogDescription",e$=d.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eO(eQ,n);return(0,E.jsx)(y.WV.p,{id:o.descriptionId,...r,ref:t})});e$.displayName=eQ;var e0="DialogClose",e1=d.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eO(e0,n);return(0,E.jsx)(y.WV.button,{type:"button",...r,ref:t,onClick:(0,f.M)(e.onClick,()=>o.onOpenChange(!1))})});function e2(e){return e?"open":"closed"}e1.displayName=e0;var e6="DialogTitleWarning",[e5,e8]=(0,h.k)(e6,{contentName:eX,titleName:eG,docsSlug:"dialog"}),e3=e=>{let{titleId:t}=e,n=e8(e6),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return d.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},e7=e=>{let{contentRef:t,descriptionId:n}=e,r=e8("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return d.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},e4=eL,e9=eF,te=eB,tt=ez,tn=eY,tr=eJ,to=e$,ta=e1},8614:function(e,t,n){n.d(t,{M:function(){return g}});var r=n(7437),o=n(2265),a=n(8881),i=n(3576),c=n(4252),u=n(5750);class l extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function s(e){let{children:t,isPresent:n}=e,a=(0,o.useId)(),i=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:s}=(0,o.useContext)(u._);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:o}=c.current;if(n||!i.current||!e||!t)return;i.current.dataset.motionPopId=a;let u=document.createElement("style");return s&&(u.nonce=s),document.head.appendChild(u),u.sheet&&u.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(u)}},[n]),(0,r.jsx)(l,{isPresent:n,childRef:i,sizeRef:c,children:o.cloneElement(t,{ref:i})})}let d=e=>{let{children:t,initial:n,isPresent:a,onExitComplete:u,custom:l,presenceAffectsLayout:d,mode:p}=e,h=(0,i.h)(f),v=(0,o.useId)(),m=(0,o.useCallback)(e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;u&&u()},[h,u]),g=(0,o.useMemo)(()=>({id:v,initial:n,isPresent:a,custom:l,onExitComplete:m,register:e=>(h.set(e,!1),()=>h.delete(e))}),d?[Math.random(),m]:[a,m]);return(0,o.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[a]),o.useEffect(()=>{a||h.size||!u||u()},[a]),"popLayout"===p&&(t=(0,r.jsx)(s,{isPresent:a,children:t})),(0,r.jsx)(c.O.Provider,{value:g,children:t})};function f(){return new Map}var p=n(9637);let h=e=>e.key||"";function v(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var m=n(1534);let g=e=>{let{children:t,custom:n,initial:c=!0,onExitComplete:u,presenceAffectsLayout:l=!0,mode:s="sync",propagate:f=!1}=e,[g,y]=(0,p.oO)(f),b=(0,o.useMemo)(()=>v(t),[t]),E=f&&!g?[]:b.map(h),w=(0,o.useRef)(!0),x=(0,o.useRef)(b),C=(0,i.h)(()=>new Map),[R,M]=(0,o.useState)(b),[S,k]=(0,o.useState)(b);(0,m.L)(()=>{w.current=!1,x.current=b;for(let e=0;e<S.length;e++){let t=h(S[e]);E.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[S,E.length,E.join("-")]);let j=[];if(b!==R){let e=[...b];for(let t=0;t<S.length;t++){let n=S[t],r=h(n);E.includes(r)||(e.splice(t,0,n),j.push(n))}"wait"===s&&j.length&&(e=j),k(v(e)),M(b);return}let{forceRender:N}=(0,o.useContext)(a.p);return(0,r.jsx)(r.Fragment,{children:S.map(e=>{let t=h(e),o=(!f||!!g)&&(b===S||E.includes(t));return(0,r.jsx)(d,{isPresent:o,initial:(!w.current||!!c)&&void 0,custom:o?void 0:n,presenceAffectsLayout:l,mode:s,onExitComplete:o?void 0:()=>{if(!C.has(t))return;C.set(t,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(null==N||N(),k(x.current),f&&(null==y||y()),u&&u())},children:e},t)})})}},1146:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(6471).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},3459:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(6471).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3295:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(6471).Z)("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},4986:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(6471).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);