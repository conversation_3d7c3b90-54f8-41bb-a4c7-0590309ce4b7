"use strict";exports.id=915,exports.ids=[915],exports.modules={6226:(e,t,n)=>{n.d(t,{default:()=>i.a});var r=n(9029),i=n.n(r)},2481:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return v}});let r=n(1174),i=n(8374),o=n(326),l=i._(n(7577)),a=r._(n(962)),s=r._(n(815)),u=n(119),f=n(5248),c=n(1206);n(576);let d=n(131),p=r._(n(6820)),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,n,r,i,o,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,i=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function m(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,l.forwardRef)((e,t)=>{let{src:n,srcSet:r,sizes:i,height:a,width:s,decoding:u,className:f,style:c,fetchPriority:d,placeholder:p,loading:h,unoptimized:y,fill:x,onLoadRef:v,onLoadingCompleteRef:w,setBlurComplete:b,setShowAltText:C,sizesInput:R,onLoad:E,onError:S,...j}=e;return(0,o.jsx)("img",{...j,...m(d),loading:h,width:s,height:a,decoding:u,"data-nimg":x?"fill":"1",className:f,style:c,sizes:i,srcSet:r,src:n,ref:(0,l.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(S&&(e.src=e.src),e.complete&&g(e,p,v,w,b,y,R))},[n,p,v,w,b,S,y,R,t]),onLoad:e=>{g(e.currentTarget,p,v,w,b,y,R)},onError:e=>{C(!0),"empty"!==p&&b(!0),S&&S(e)}})});function x(e){let{isAppRouter:t,imgAttributes:n}=e,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...m(n.fetchPriority)};return t&&a.default.preload?(a.default.preload(n.src,r),null):(0,o.jsx)(s.default,{children:(0,o.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let v=(0,l.forwardRef)((e,t)=>{let n=(0,l.useContext)(d.RouterContext),r=(0,l.useContext)(c.ImageConfigContext),i=(0,l.useMemo)(()=>{var e;let t=h||r||f.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:i,qualities:o}},[r]),{onLoad:a,onLoadingComplete:s}=e,g=(0,l.useRef)(a);(0,l.useEffect)(()=>{g.current=a},[a]);let m=(0,l.useRef)(s);(0,l.useEffect)(()=>{m.current=s},[s]);let[v,w]=(0,l.useState)(!1),[b,C]=(0,l.useState)(!1),{props:R,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:v,showAltText:b});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y,{...R,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:g,onLoadingCompleteRef:m,setBlurComplete:w,setShowAltText:C,sizesInput:e.sizes,ref:t}),E.priority?(0,o.jsx)(x,{isAppRouter:!n,imgAttributes:R}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3484:(e,t,n)=>{e.exports=n(1616).vendored.contexts.AmpContext},1157:(e,t,n)=>{e.exports=n(1616).vendored.contexts.HeadManagerContext},1206:(e,t,n)=>{e.exports=n(1616).vendored.contexts.ImageConfigContext},8710:(e,t)=>{function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||n&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},119:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),n(576);let r=n(380),i=n(5248);function o(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var n,a;let s,u,f,{src:c,sizes:d,unoptimized:p=!1,priority:h=!1,loading:g,className:m,quality:y,width:x,height:v,fill:w=!1,style:b,overrideSrc:C,onLoad:R,onLoadingComplete:E,placeholder:S="empty",blurDataURL:j,fetchPriority:P,decoding:A="async",layout:O,objectFit:T,objectPosition:_,lazyBoundary:M,lazyRoot:L,...k}=e,{imgConf:z,showAltText:D,blurComplete:I,defaultLoader:W}=t,F=z||i.imageConfigDefault;if("allSizes"in F)s=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),r=null==(n=F.qualities)?void 0:n.sort((e,t)=>e-t);s={...F,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===W)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let N=k.loader||W;delete k.loader,delete k.srcSet;let H="__next_img_default"in N;if(H){if("custom"===s.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=N;N=t=>{let{config:n,...r}=t;return e(r)}}if(O){"fill"===O&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!d&&(d=t)}let V="",B=l(x),$=l(v);if("object"==typeof(a=c)&&(o(a)||void 0!==a.src)){let e=o(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,f=e.blurHeight,j=j||e.blurDataURL,V=e.src,!w){if(B||$){if(B&&!$){let t=B/e.width;$=Math.round(e.height*t)}else if(!B&&$){let t=$/e.height;B=Math.round(e.width*t)}}else B=e.width,$=e.height}}let U=!h&&("lazy"===g||void 0===g);(!(c="string"==typeof c?c:V)||c.startsWith("data:")||c.startsWith("blob:"))&&(p=!0,U=!1),s.unoptimized&&(p=!0),H&&c.endsWith(".svg")&&!s.dangerouslyAllowSVG&&(p=!0),h&&(P="high");let Y=l(y),q=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:T,objectPosition:_}:{},D?{}:{color:"transparent"},b),G=I||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:B,heightInt:$,blurWidth:u,blurHeight:f,blurDataURL:j||"",objectFit:q.objectFit})+'")':'url("'+S+'")',X=G?{backgroundSize:q.objectFit||"cover",backgroundPosition:q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:G}:{},Z=function(e){let{config:t,src:n,unoptimized:r,width:i,quality:o,sizes:l,loader:a}=e;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,n){let{deviceSizes:r,allSizes:i}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(n);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,l),f=s.length-1;return{sizes:l||"w"!==u?l:"100vw",srcSet:s.map((e,r)=>a({config:t,src:n,quality:o,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:a({config:t,src:n,quality:o,width:s[f]})}}({config:s,src:c,unoptimized:p,width:B,quality:Y,sizes:d,loader:N});return{props:{...k,loading:U?"lazy":g,fetchPriority:P,width:B,height:$,decoding:A,className:m,style:{...q,...X},sizes:Z.sizes,srcSet:Z.srcSet,src:C||Z.src},meta:{unoptimized:p,priority:h,placeholder:S,fill:w}}}},815:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},defaultHead:function(){return c}});let r=n(1174),i=n(8374),o=n(326),l=i._(n(7577)),a=r._(n(8003)),s=n(3484),u=n(1157),f=n(8710);function c(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(576);let p=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:n}=t;return e.reduce(d,[]).reverse().concat(c(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return i=>{let o=!0,l=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){l=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=i.props[t],n=r[t]||new Set;("name"!==t||!l)&&n.has(e)?o=!1:(n.add(e),r[t]=n)}}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,l.default.cloneElement(e,t)}return l.default.cloneElement(e,{key:r})})}let g=function(e){let{children:t}=e,n=(0,l.useContext)(s.AmpStateContext),r=(0,l.useContext)(u.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:h,headManager:r,inAmpMode:(0,f.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},380:(e,t)=>{function n(e){let{widthInt:t,heightInt:n,blurWidth:r,blurHeight:i,blurDataURL:o,objectFit:l}=e,a=r?40*r:t,s=i?40*i:n,u=a&&s?"viewBox='0 0 "+a+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},5248:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},9029:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return s},getImageProps:function(){return a}});let r=n(1174),i=n(119),o=n(2481),l=r._(n(6820));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let s=o.Image},6820:(e,t)=>{function n(e){var t;let{config:n,src:r,width:i,quality:o}=e,l=o||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+l}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},8003:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(7577),i=()=>{},o=()=>{};function l(e){var t;let{headManager:n,reduceComponentsToState:l}=e;function a(){if(n&&n.mountedInstances){let t=r.Children.toArray(Array.from(n.mountedInstances).filter(Boolean));n.updateHead(l(t,e))}}return null==n||null==(t=n.mountedInstances)||t.add(e.children),a(),i(()=>{var t;return null==n||null==(t=n.mountedInstances)||t.add(e.children),()=>{var t;null==n||null==(t=n.mountedInstances)||t.delete(e.children)}}),i(()=>(n&&(n._pendingUpdate=a),()=>{n&&(n._pendingUpdate=a)})),o(()=>(n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null),()=>{n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null)})),null}},8957:(e,t,n)=>{n.d(t,{M:()=>s});var r,i=n(7577),o=n(5819),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function s(e){let[t,n]=i.useState(l());return(0,o.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},9469:(e,t,n)=>{n.d(t,{g7:()=>l});var r=n(7577);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=n(326),l=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,a=r.Children.toArray(i),u=a.find(s);if(u){let e=u.props.children,i=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}("Slot"),a=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},9582:(e,t,n)=>{n.d(t,{VY:()=>tv,zt:()=>tm,fC:()=>ty,xz:()=>tx});var r=n(7577),i=n(2561),o=n(8051),l=n(3095),a=n(825),s=n(8957);let u=["top","right","bottom","left"],f=Math.min,c=Math.max,d=Math.round,p=Math.floor,h=e=>({x:e,y:e}),g={left:"right",right:"left",bottom:"top",top:"bottom"},m={start:"end",end:"start"};function y(e,t){return"function"==typeof e?e(t):e}function x(e){return e.split("-")[0]}function v(e){return e.split("-")[1]}function w(e){return"x"===e?"y":"x"}function b(e){return"y"===e?"height":"width"}function C(e){return["top","bottom"].includes(x(e))?"y":"x"}function R(e){return e.replace(/start|end/g,e=>m[e])}function E(e){return e.replace(/left|right|bottom|top/g,e=>g[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function j(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function P(e,t,n){let r,{reference:i,floating:o}=e,l=C(t),a=w(C(t)),s=b(a),u=x(t),f="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,p=i[s]/2-o[s]/2;switch(u){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(v(t)){case"start":r[a]-=p*(n&&f?-1:1);break;case"end":r[a]+=p*(n&&f?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=P(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:g}=a[n],{x:m,y:y,data:x,reset:v}=await g({x:f,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=m?m:f,c=null!=y?y:c,p={...p,[o]:{...p[o],...x}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):v.rects),{x:f,y:c}=P(u,d,s)),n=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:d=!1,padding:p=0}=y(t,e),h=S(p),g=a[d?"floating"===c?"reference":"floating":c],m=j(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:s})),x="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),w=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},b=j(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:v,strategy:s}):x);return{top:(m.top-b.top+h.top)/w.y,bottom:(b.bottom-m.bottom+h.bottom)/w.y,left:(m.left-b.left+h.left)/w.x,right:(b.right-m.right+h.right)/w.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _(e){return u.some(t=>e[t]>=0)}async function M(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=x(n),a=v(n),s="y"===C(n),u=["left","top"].includes(l)?-1:1,f=o&&s?-1:1,c=y(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*f,y:d*u}:{x:d*u,y:p*f}}function L(){return"undefined"!=typeof window}function k(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return!!L()&&(e instanceof Node||e instanceof z(e).Node)}function W(e){return!!L()&&(e instanceof Element||e instanceof z(e).Element)}function F(e){return!!L()&&(e instanceof HTMLElement||e instanceof z(e).HTMLElement)}function N(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof z(e).ShadowRoot)}function H(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=Y(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function V(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=$(),n=W(e)?Y(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function $(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function U(e){return["html","body","#document"].includes(k(e))}function Y(e){return z(e).getComputedStyle(e)}function q(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function G(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||D(e);return N(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=G(t);return U(n)?t.ownerDocument?t.ownerDocument.body:t.body:F(n)&&H(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=z(i);if(o){let e=Z(l);return t.concat(l,l.visualViewport||[],H(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function J(e){let t=Y(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=F(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,a=d(n)!==o||d(r)!==l;return a&&(n=o,r=l),{width:n,height:r,$:a}}function K(e){return W(e)?e:e.contextElement}function Q(e){let t=K(e);if(!F(t))return h(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=J(t),l=(o?d(n.width):n.width)/r,a=(o?d(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ee=h(0);function et(e){let t=z(e);return $()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function en(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=K(e),a=h(1);t&&(r?W(r)&&(a=Q(r)):a=Q(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===z(l))&&i)?et(l):h(0),u=(o.left+s.x)/a.x,f=(o.top+s.y)/a.y,c=o.width/a.x,d=o.height/a.y;if(l){let e=z(l),t=r&&W(r)?z(r):r,n=e,i=Z(n);for(;i&&r&&t!==n;){let e=Q(i),t=i.getBoundingClientRect(),r=Y(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,f*=e.y,c*=e.x,d*=e.y,u+=o,f+=l,i=Z(n=z(i))}}return j({width:c,height:d,x:u,y:f})}function er(e,t){let n=q(e).scrollLeft;return t?t.left+n:en(D(e)).left+n}function ei(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:er(e,r)),y:r.top+t.scrollTop}}function eo(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=z(e),r=D(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=$();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=q(e),r=e.ownerDocument.body,i=c(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=c(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+er(e),a=-n.scrollTop;return"rtl"===Y(r).direction&&(l+=c(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:l,y:a}}(D(e));else if(W(t))r=function(e,t){let n=en(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=F(e)?Q(e):h(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=et(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return j(r)}function el(e){return"static"===Y(e).position}function ea(e,t){if(!F(e)||"fixed"===Y(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function es(e,t){let n=z(e);if(V(e))return n;if(!F(e)){let t=G(e);for(;t&&!U(t);){if(W(t)&&!el(t))return t;t=G(t)}return n}let r=ea(e,t);for(;r&&["table","td","th"].includes(k(r))&&el(r);)r=ea(r,t);return r&&U(r)&&el(r)&&!B(r)?n:r||function(e){let t=G(e);for(;F(t)&&!U(t);){if(B(t))return t;if(V(t))break;t=G(t)}return null}(e)||n}let eu=async function(e){let t=this.getOffsetParent||es,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=F(t),i=D(t),o="fixed"===n,l=en(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=h(0);if(r||!r&&!o){if(("body"!==k(t)||H(i))&&(a=q(t)),r){let e=en(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=er(i))}let u=!i||r||o?h(0):ei(i,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ef={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=D(r),a=!!t&&V(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},u=h(1),f=h(0),c=F(r);if((c||!c&&!o)&&(("body"!==k(r)||H(l))&&(s=q(r)),F(r))){let e=en(r);u=Q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let d=!l||c||o?h(0):ei(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+f.x+d.x,y:n.y*u.y-s.scrollTop*u.y+f.y+d.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>W(e)&&"body"!==k(e)),i=null,o="fixed"===Y(e).position,l=o?G(e):e;for(;W(l)&&!U(l);){let t=Y(l),n=B(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||H(l)&&!n&&function e(t,n){let r=G(t);return!(r===n||!W(r)||U(r))&&("fixed"===Y(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=G(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=o[0],a=o.reduce((e,n)=>{let r=eo(t,n,i);return e.top=c(r.top,e.top),e.right=f(r.right,e.right),e.bottom=f(r.bottom,e.bottom),e.left=c(r.left,e.left),e},eo(t,l,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:es,getElementRects:eu,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=J(e);return{width:t,height:n}},getScale:Q,isElement:W,isRTL:function(e){return"rtl"===Y(e).direction}};function ec(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:d=0}=y(e,t)||{};if(null==u)return{};let p=S(d),h={x:n,y:r},g=w(C(i)),m=b(g),x=await l.getDimensions(u),R="y"===g,E=R?"clientHeight":"clientWidth",j=o.reference[m]+o.reference[g]-h[g]-o.floating[m],P=h[g]-o.reference[g],A=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),O=A?A[E]:0;O&&await (null==l.isElement?void 0:l.isElement(A))||(O=a.floating[E]||o.floating[m]);let T=O/2-x[m]/2-1,_=f(p[R?"top":"left"],T),M=f(p[R?"bottom":"right"],T),L=O-x[m]-M,k=O/2-x[m]/2+(j/2-P/2),z=c(_,f(k,L)),D=!s.arrow&&null!=v(i)&&k!==z&&o.reference[m]/2-(k<_?_:M)-x[m]/2<0,I=D?k<_?k-_:k-L:0;return{[g]:h[g]+I,data:{[g]:z,centerOffset:k-z-I,...D&&{alignmentOffset:I}},reset:D}}}),ep=(e,t,n)=>{let r=new Map,i={platform:ef,...n},o={...i.platform,_c:r};return A(e,t,{...i,platform:o})};var eh=n(962),eg="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function em(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!em(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!em(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ey(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ex(e,t){let n=ey(e);return Math.round(t*n)/n}function ev(e){let t=r.useRef(e);return eg(()=>{t.current=e}),t}let ew=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ed({element:n.current,padding:r}).fn(t):{}:n?ed({element:n,padding:r}).fn(t):{}}}),eb=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,s=await M(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:o=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=y(e,t),u={x:n,y:r},d=await O(t,s),p=C(x(i)),h=w(p),g=u[h],m=u[p];if(o){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=g+d[e],r=g-d[t];g=c(n,f(g,r))}if(l){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=m+d[e],r=m-d[t];m=c(n,f(m,r))}let v=a.fn({...t,[h]:g,[p]:m});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[h]:o,[p]:l}}}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=y(e,t),f={x:n,y:r},c=C(i),d=w(c),p=f[d],h=f[c],g=y(a,t),m="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(s){let e="y"===d?"height":"width",t=o.reference[d]-o.floating[e]+m.mainAxis,n=o.reference[d]+o.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,b;let e="y"===d?"width":"height",t=["top","left"].includes(x(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(v=l.offset)?void 0:v[c])||0)+(t?0:m.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[c])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[c]:h}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:f,platform:c,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:g,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:j=!0,...P}=y(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let A=x(a),T=C(f),_=x(f)===f,M=await (null==c.isRTL?void 0:c.isRTL(d.floating)),L=g||(_||!j?[E(f)]:function(e){let t=E(e);return[R(e),t,R(t)]}(f)),k="none"!==S;!g&&k&&L.push(...function(e,t,n,r){let i=v(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(x(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(R)))),o}(f,j,S,M));let z=[f,...L],D=await O(t,P),I=[],W=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&I.push(D[A]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=v(e),i=w(C(e)),o=b(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=E(l)),[l,E(l)]}(a,u,M);I.push(D[e[0]],D[e[1]])}if(W=[...W,{placement:a,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=z[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(o=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=W.filter(e=>{if(k){let t=C(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eS=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,o;let{placement:l,rects:a,platform:s,elements:u}=t,{apply:d=()=>{},...p}=y(e,t),h=await O(t,p),g=x(l),m=v(l),w="y"===C(l),{width:b,height:R}=a.floating;"top"===g||"bottom"===g?(i=g,o=m===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(o=g,i="end"===m?"top":"bottom");let E=R-h.top-h.bottom,S=b-h.left-h.right,j=f(R-h[i],E),P=f(b-h[o],S),A=!t.middlewareData.shift,T=j,_=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(_=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=E),A&&!m){let e=c(h.left,0),t=c(h.right,0),n=c(h.top,0),r=c(h.bottom,0);w?_=b-2*(0!==e||0!==t?e+t:c(h.left,h.right)):T=R-2*(0!==n||0!==r?n+r:c(h.top,h.bottom))}await d({...t,availableWidth:_,availableHeight:T});let M=await s.getDimensions(u.floating);return b!==M.width||R!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=y(e,t);switch(r){case"referenceHidden":{let e=T(await O(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:_(e)}}}case"escaped":{let e=T(await O(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:_(e)}}}default:return{}}}}}(e),options:[e,t]}),eP=(e,t)=>({...ew(e),options:[e,t]});var eA=n(7335),eO=n(326),eT=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eO.jsx)(eA.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eT.displayName="Arrow";var e_=n(5049),eM=n(5819),eL="Popper",[ek,ez]=(0,l.b)(eL),[eD,eI]=ek(eL),eW=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eO.jsx)(eD,{scope:t,anchor:i,onAnchorChange:o,children:n})};eW.displayName=eL;var eF="PopperAnchor",eN=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...l}=e,a=eI(eF,n),s=r.useRef(null),u=(0,o.e)(t,s);return r.useEffect(()=>{a.onAnchorChange(i?.current||s.current)}),i?null:(0,eO.jsx)(eA.WV.div,{...l,ref:u})});eN.displayName=eF;var eH="PopperContent",[eV,eB]=ek(eH),e$=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:s=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:h=[],collisionPadding:g=0,sticky:m="partial",hideWhenDetached:y=!1,updatePositionStrategy:x="optimized",onPlaced:v,...w}=e,b=eI(eH,n),[C,R]=r.useState(null),E=(0,o.e)(t,e=>R(e)),[S,j]=r.useState(null),P=function(e){let[t,n]=r.useState(void 0);return(0,eM.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(S),A=P?.width??0,O=P?.height??0,T="number"==typeof g?g:{top:0,right:0,bottom:0,left:0,...g},_=Array.isArray(h)?h:[h],M=_.length>0,L={padding:T,boundary:_.filter(eG),altBoundary:M},{refs:k,floatingStyles:z,placement:I,isPositioned:W,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:f}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);em(p,i)||h(i);let[g,m]=r.useState(null),[y,x]=r.useState(null),v=r.useCallback(e=>{e!==R.current&&(R.current=e,m(e))},[]),w=r.useCallback(e=>{e!==E.current&&(E.current=e,x(e))},[]),b=l||g,C=a||y,R=r.useRef(null),E=r.useRef(null),S=r.useRef(c),j=null!=u,P=ev(u),A=ev(o),O=ev(f),T=r.useCallback(()=>{if(!R.current||!E.current)return;let e={placement:t,strategy:n,middleware:p};A.current&&(e.platform=A.current),ep(R.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};_.current&&!em(S.current,t)&&(S.current=t,eh.flushSync(()=>{d(t)}))})},[p,t,n,A,O]);eg(()=>{!1===f&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let _=r.useRef(!1);eg(()=>(_.current=!0,()=>{_.current=!1}),[]),eg(()=>{if(b&&(R.current=b),C&&(E.current=C),b&&C){if(P.current)return P.current(b,C,T);T()}},[b,C,T,P,j]);let M=r.useMemo(()=>({reference:R,floating:E,setReference:v,setFloating:w}),[v,w]),L=r.useMemo(()=>({reference:b,floating:C}),[b,C]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=ex(L.floating,c.x),r=ex(L.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ey(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,L.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:T,refs:M,elements:L,floatingStyles:k}),[c,T,M,L,k])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,d=K(e),h=o||l?[...d?X(d):[],...X(t)]:[];h.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let g=d&&s?function(e,t){let n,r=null,i=D(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),o();let u=e.getBoundingClientRect(),{left:d,top:h,width:g,height:m}=u;if(a||t(),!g||!m)return;let y=p(h),x=p(i.clientWidth-(d+g)),v={rootMargin:-y+"px "+-x+"px "+-p(i.clientHeight-(h+m))+"px "+-p(d)+"px",threshold:c(0,f(1,s))||1},w=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!w)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||ec(u,e.getBoundingClientRect())||l(),w=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,v)}r.observe(e)}(!0),o}(d,n):null,m=-1,y=null;a&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),d&&!u&&y.observe(d),y.observe(t));let x=u?en(e):null;return u&&function t(){let r=en(e);x&&!ec(x,r)&&n(),x=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{o&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==g||g(),null==(e=y)||e.disconnect(),y=null,u&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===x}),elements:{reference:b.anchor},middleware:[eb({mainAxis:l+O,alignmentAxis:s}),d&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eR():void 0,...L}),d&&eE({...L}),eS({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),S&&eP({element:S,padding:u}),eX({arrowWidth:A,arrowHeight:O}),y&&ej({strategy:"referenceHidden",...L})]}),[N,H]=eZ(I),V=(0,e_.W)(v);(0,eM.b)(()=>{W&&V?.()},[W,V]);let B=F.arrow?.x,$=F.arrow?.y,U=F.arrow?.centerOffset!==0,[Y,q]=r.useState();return(0,eM.b)(()=>{C&&q(window.getComputedStyle(C).zIndex)},[C]),(0,eO.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:W?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eV,{scope:n,placedSide:N,onArrowChange:j,arrowX:B,arrowY:$,shouldHideArrow:U,children:(0,eO.jsx)(eA.WV.div,{"data-side":N,"data-align":H,...w,ref:E,style:{...w.style,animation:W?void 0:"none"}})})})});e$.displayName=eH;var eU="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eq=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eB(eU,n),o=eY[i.placedSide];return(0,eO.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(eT,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}eq.displayName=eU;var eX=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,l=o?0:e.arrowWidth,a=o?0:e.arrowHeight,[s,u]=eZ(n),f={start:"0%",center:"50%",end:"100%"}[u],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=o?f:`${c}px`,h=`${-a}px`):"top"===s?(p=o?f:`${c}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=o?f:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=o?f:`${d}px`),{data:{x:p,y:h}}}});function eZ(e){let[t,n="center"]=e.split("-");return[t,n]}n(3078);var eJ=n(9815);r.forwardRef((e,t)=>{let{children:n,...i}=e,o=r.Children.toArray(n),l=o.find(e0);if(l){let e=l.props.children,n=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,eO.jsx)(eK,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,eO.jsx)(eK,{...i,ref:t,children:n})}).displayName="Slot";var eK=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),l=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(l.ref=t?(0,o.F)(t,e):e),r.cloneElement(n,l)}return r.Children.count(n)>1?r.Children.only(null):null});eK.displayName="SlotClone";var eQ=({children:e})=>(0,eO.jsx)(eO.Fragment,{children:e});function e0(e){return r.isValidElement(e)&&e.type===eQ}var e1=n(2067),e2=n(6009),[e5,e3]=(0,l.b)("Tooltip",[ez]),e8=ez(),e4="TooltipProvider",e6="tooltip.open",[e7,e9]=e5(e4),te=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:l}=e,[a,s]=r.useState(!0),u=r.useRef(!1),f=r.useRef(0);return r.useEffect(()=>{let e=f.current;return()=>window.clearTimeout(e)},[]),(0,eO.jsx)(e7,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(f.current),s(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>s(!0),i)},[i]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:o,children:l})};te.displayName=e4;var tt="Tooltip",[tn,tr]=e5(tt),ti=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,f=e9(tt,e.__scopeTooltip),c=e8(t),[d,p]=r.useState(null),h=(0,s.M)(),g=r.useRef(0),m=a??f.disableHoverableContent,y=u??f.delayDuration,x=r.useRef(!1),[v=!1,w]=(0,e1.T)({prop:i,defaultProp:o,onChange:e=>{e?(f.onOpen(),document.dispatchEvent(new CustomEvent(e6))):f.onClose(),l?.(e)}}),b=r.useMemo(()=>v?x.current?"delayed-open":"instant-open":"closed",[v]),C=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,x.current=!1,w(!0)},[w]),R=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,w(!1)},[w]),E=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{x.current=!0,w(!0),g.current=0},y)},[y,w]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,eO.jsx)(eW,{...c,children:(0,eO.jsx)(tn,{scope:t,contentId:h,open:v,stateAttribute:b,trigger:d,onTriggerChange:p,onTriggerEnter:r.useCallback(()=>{f.isOpenDelayed?E():C()},[f.isOpenDelayed,E,C]),onTriggerLeave:r.useCallback(()=>{m?R():(window.clearTimeout(g.current),g.current=0)},[R,m]),onOpen:C,onClose:R,disableHoverableContent:m,children:n})})};ti.displayName=tt;var to="TooltipTrigger",tl=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=tr(to,n),s=e9(to,n),u=e8(n),f=r.useRef(null),c=(0,o.e)(t,f,a.onTriggerChange),d=r.useRef(!1),p=r.useRef(!1),h=r.useCallback(()=>d.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,eO.jsx)(eN,{asChild:!0,...u,children:(0,eO.jsx)(eA.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:c,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||p.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),p.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),p.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:(0,i.M)(e.onBlur,a.onClose),onClick:(0,i.M)(e.onClick,a.onClose)})})});tl.displayName=to;var[ta,ts]=e5("TooltipPortal",{forceMount:void 0}),tu="TooltipContent",tf=r.forwardRef((e,t)=>{let n=ts(tu,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,l=tr(tu,e.__scopeTooltip);return(0,eO.jsx)(eJ.z,{present:r||l.open,children:l.disableHoverableContent?(0,eO.jsx)(th,{side:i,...o,ref:t}):(0,eO.jsx)(tc,{side:i,...o,ref:t})})}),tc=r.forwardRef((e,t)=>{let n=tr(tu,e.__scopeTooltip),i=e9(tu,e.__scopeTooltip),l=r.useRef(null),a=(0,o.e)(t,l),[s,u]=r.useState(null),{trigger:f,onClose:c}=n,d=l.current,{onPointerInTransitChange:p}=i,h=r.useCallback(()=>{u(null),p(!1)},[p]),g=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(f&&d){let e=e=>g(e,d),t=e=>g(e,f);return f.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{f.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[f,d,g,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=f?.contains(t)||d?.contains(t),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let l=t[e].x,a=t[e].y,s=t[o].x,u=t[o].y;a>r!=u>r&&n<(s-l)*(r-a)/(u-a)+l&&(i=!i)}return i}(n,s);r?h():i&&(h(),c())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[f,d,s,c,h]),(0,eO.jsx)(th,{...e,ref:a})}),[td,tp]=e5(tt,{isInside:!1}),th=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:l,onPointerDownOutside:s,...u}=e,f=tr(tu,n),c=e8(n),{onClose:d}=f;return r.useEffect(()=>(document.addEventListener(e6,d),()=>document.removeEventListener(e6,d)),[d]),r.useEffect(()=>{if(f.trigger){let e=e=>{let t=e.target;t?.contains(f.trigger)&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[f.trigger,d]),(0,eO.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:(0,eO.jsxs)(e$,{"data-state":f.stateAttribute,...c,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eO.jsx)(eQ,{children:i}),(0,eO.jsx)(td,{scope:n,isInside:!0,children:(0,eO.jsx)(e2.f,{id:f.contentId,role:"tooltip",children:o||i})})]})})});tf.displayName=tu;var tg="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=e8(n);return tp(tg,n).isInside?null:(0,eO.jsx)(eq,{...i,...r,ref:t})}).displayName=tg;var tm=te,ty=ti,tx=tl,tv=tf},342:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(9664).Z)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])}};