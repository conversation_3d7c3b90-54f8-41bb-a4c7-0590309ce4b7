"use strict";exports.id=864,exports.ids=[864],exports.modules={434:(e,t,r)=>{r.d(t,{default:()=>i.a});var n=r(9404),i=r.n(n)},5047:(e,t,r)=>{var n=r(7389);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}})},3486:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(8974),i=r(3658);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3416:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(3658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5424:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return i}});let n=r(2994);async function i(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,i)=>{r({actionId:e,actionArgs:t,resolve:n,reject:i})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8038:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(7577),i=r(962),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5138:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return l},NEXT_DID_POSTPONE_HEADER:function(){return c},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_RSC_UNION_QUERY:function(){return u},NEXT_URL:function(){return a},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Url",s="text/x-component",l=[[r],[i],[o]],u="_rsc",c="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2994:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return M},default:function(){return D},getServerActionDispatcher:function(){return E},urlToUrlWithoutFlightMarker:function(){return T}});let n=r(8374),i=r(326),o=n._(r(7577)),a=r(2413),s=r(7767),l=r(7584),u=r(7008),c=r(7326),d=r(9727),f=r(6199),h=r(2148),p=r(3486),m=r(8038),y=r(6265),g=r(2492),v=r(9519),b=r(5138),x=r(4237),P=r(7929),_=r(8071),w=null,R=null;function E(){return R}let S={};function T(e){let t=new URL(e,location.origin);if(t.searchParams.delete(b.NEXT_RSC_UNION_QUERY),t.pathname.endsWith(".txt")){let{pathname:e}=t,r=e.endsWith("/index.txt")?10:4;t.pathname=e.slice(0,-r)}return t}function j(e){return e.origin!==window.location.origin}function O(e){let{appRouterState:t,sync:r}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:i}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==i?(n.pendingPush=!1,window.history.pushState(o,"",i)):window.history.replaceState(o,"",i),r(t)},[t,r]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null}}function A(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function N(e){let t,{buildId:r,initialHead:n,initialTree:l,urlParts:d,initialSeedData:b,couldBeIntercepted:E,assetPrefix:T,missingSlots:M}=e,N=(0,o.useMemo)(()=>(0,f.createInitialRouterState)({buildId:r,initialSeedData:b,urlParts:d,initialTree:l,initialParallelRoutes:w,location:null,initialHead:n,couldBeIntercepted:E}),[r,b,d,l,n,E]),[D,k,I]=(0,c.useReducerWithReduxDevtools)(N);(0,o.useEffect)(()=>{w=null},[]);let{canonicalUrl:L}=(0,c.useUnwrapState)(D),{searchParams:F,pathname:U}=(0,o.useMemo)(()=>{let e=new URL(L,"http://n");return{searchParams:e.searchParams,pathname:(0,P.hasBasePath)(e.pathname)?(0,x.removeBasePath)(e.pathname):e.pathname}},[L]),V=(0,o.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,o.startTransition)(()=>{k({type:s.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[k]),B=(0,o.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return k({type:s.ACTION_NAVIGATE,url:n,isExternalUrl:j(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[k]);R=(0,o.useCallback)(e=>{(0,o.startTransition)(()=>{k({...e,type:s.ACTION_SERVER_ACTION})})},[k]);let W=(0,o.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r;if(!(0,h.isBot)(window.navigator.userAgent)){try{r=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL.")}j(r)||(0,o.startTransition)(()=>{var e;k({type:s.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:s.PrefetchKind.FULL})})}},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;B(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;B(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,o.startTransition)(()=>{k({type:s.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[k,B]);(0,o.useEffect)(()=>{window.next&&(window.next.router=W)},[W]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(S.pendingMpaPath=void 0,k({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[k]);let{pushRef:z}=(0,c.useUnwrapState)(D);if(z.mpaNavigation){if(S.pendingMpaPath!==L){let e=window.location;z.pendingPush?e.assign(L):e.replace(L),S.pendingMpaPath=L}(0,o.use)(v.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{k({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=A(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=A(e),i&&r(i)),t(e,n,i)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,o.startTransition)(()=>{k({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[k]);let{cache:H,tree:$,nextUrl:G,focusAndScrollRef:K}=(0,c.useUnwrapState)(D),X=(0,o.useMemo)(()=>(0,g.findHeadInCache)(H,$[1]),[H,$]),Y=(0,o.useMemo)(()=>(function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),o=i?t[1]:t;!o||o.startsWith(_.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r})($),[$]);if(null!==X){let[e,r]=X;t=(0,i.jsx)(C,{headCacheNode:e},r)}else t=null;let q=(0,i.jsxs)(y.RedirectBoundary,{children:[t,H.rsc,(0,i.jsx)(m.AppRouterAnnouncer,{tree:$})]});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(O,{appRouterState:(0,c.useUnwrapState)(D),sync:I}),(0,i.jsx)(u.PathParamsContext.Provider,{value:Y,children:(0,i.jsx)(u.PathnameContext.Provider,{value:U,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:F,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:V,tree:$,focusAndScrollRef:K,nextUrl:G},children:(0,i.jsx)(a.AppRouterContext.Provider,{value:W,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{childNodes:H.parallelRoutes,tree:$,url:L,loading:H.loading},children:q})})})})})})]})}function D(e){let{globalErrorComponent:t,...r}=e;return(0,i.jsx)(d.ErrorBoundary,{errorComponent:t,children:(0,i.jsx)(N,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6136:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(4129),i=r(5869);function o(e){let t=i.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6114:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let n=r(326),i=r(3325);function o(e){let{Component:t,props:r}=e;return r.searchParams=(0,i.createDynamicallyTrackedSearchParams)(r.searchParams||{}),(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9727:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return p},ErrorBoundaryHandler:function(){return d},GlobalError:function(){return f},default:function(){return h}});let n=r(1174),i=r(326),o=n._(r(7577)),a=r(7389),s=r(7313),l=r(5869),u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function c(e){let{error:t}=e,r=l.staticGenerationAsyncStorage.getStore();if((null==r?void 0:r.isRevalidate)||(null==r?void 0:r.isStaticGeneration))throw console.error(t),t;return null}class d extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,i.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,i.jsxs)("html",{id:"__next_error__",children:[(0,i.jsx)("head",{}),(0,i.jsxs)("body",{children:[(0,i.jsx)(c,{error:t}),(0,i.jsx)("div",{style:u.error,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:u.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,i.jsx)("p",{style:u.text,children:"Digest: "+r}):null]})})]})]})}let h=f;function p(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:o}=e,s=(0,a.usePathname)();return t?(0,i.jsx)(d,{pathname:s,errorComponent:t,errorStyles:r,errorScripts:n,children:o}):(0,i.jsx)(i.Fragment,{children:o})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},442:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return i}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7313:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(706),i=r(2747);function o(e){return e&&e.digest&&((0,i.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9671:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return w}}),r(1174);let n=r(8374),i=r(326),o=n._(r(7577));r(962);let a=r(2413),s=r(9009),l=r(9519),u=r(9727),c=r(455),d=r(9976),f=r(6265),h=r(1868),p=r(2162),m=r(9886),y=r(5262),g=["bottom","height","left","right","top","width","x","y"];function v(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class b extends o.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,c.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return g.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,d.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!v(r,t)&&(e.scrollTop=0,v(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function x(e){let{segmentPath:t,children:r}=e,n=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return(0,i.jsx)(b,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function P(e){let{parallelRouterKey:t,url:r,childNodes:n,segmentPath:u,tree:d,cacheKey:f}=e,h=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!h)throw Error("invariant global layout router not mounted");let{buildId:p,changeByServerResponse:m,tree:g}=h,v=n.get(f);if(void 0===v){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};v=e,n.set(f,e)}let b=null!==v.prefetchRsc?v.prefetchRsc:v.rsc,x=(0,o.useDeferredValue)(v.rsc,b),P="object"==typeof x&&null!==x&&"function"==typeof x.then?(0,o.use)(x):x;if(!P){let e=v.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,i]=t,o=2===t.length;if((0,c.matchSegment)(r[0],n)&&r[1].hasOwnProperty(i)){if(o){let t=e(void 0,r[1][i]);return[r[0],{...r[1],[i]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[i]:e(t.slice(2),r[1][i])}]}}return r}(["",...u],g),n=(0,y.hasInterceptionRouteInCurrentTree)(g);v.lazyData=e=(0,s.fetchServerResponse)(new URL(r,location.origin),t,n?h.nextUrl:null,p),v.lazyDataResolved=!1}let t=(0,o.use)(e);v.lazyDataResolved||(setTimeout(()=>{(0,o.startTransition)(()=>{m({previousTree:g,serverResponse:t})})}),v.lazyDataResolved=!0),(0,o.use)(l.unresolvedThenable)}return(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:v.parallelRoutes,url:r,loading:v.loading},children:P})}function _(e){let{children:t,hasLoading:r,loading:n,loadingStyles:a,loadingScripts:s}=e;return r?(0,i.jsx)(o.Suspense,{fallback:(0,i.jsxs)(i.Fragment,{children:[a,s,n]}),children:t}):(0,i.jsx)(i.Fragment,{children:t})}function w(e){let{parallelRouterKey:t,segmentPath:r,error:n,errorStyles:s,errorScripts:l,templateStyles:c,templateScripts:d,template:y,notFound:g,notFoundStyles:v}=e,b=(0,o.useContext)(a.LayoutRouterContext);if(!b)throw Error("invariant expected layout router to be mounted");let{childNodes:w,tree:R,url:E,loading:S}=b,T=w.get(t);T||(T=new Map,w.set(t,T));let j=R[1][t][0],O=(0,p.getSegmentValue)(j),M=[j];return(0,i.jsx)(i.Fragment,{children:M.map(e=>{let o=(0,p.getSegmentValue)(e),b=(0,m.createRouterCacheKey)(e);return(0,i.jsxs)(a.TemplateContext.Provider,{value:(0,i.jsx)(x,{segmentPath:r,children:(0,i.jsx)(u.ErrorBoundary,{errorComponent:n,errorStyles:s,errorScripts:l,children:(0,i.jsx)(_,{hasLoading:!!S,loading:null==S?void 0:S[0],loadingStyles:null==S?void 0:S[1],loadingScripts:null==S?void 0:S[2],children:(0,i.jsx)(h.NotFoundBoundary,{notFound:g,notFoundStyles:v,children:(0,i.jsx)(f.RedirectBoundary,{children:(0,i.jsx)(P,{parallelRouterKey:t,url:E,tree:R,childNodes:T,segmentPath:r,cacheKey:b,isActive:O===o})})})})})}),children:[c,d,y]},(0,m.createRouterCacheKey)(e,!0))})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},455:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{canSegmentBeOverridden:function(){return o},matchSegment:function(){return i}});let n=r(2357),i=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],o=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7389:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l.ReadonlyURLSearchParams},RedirectType:function(){return l.RedirectType},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},notFound:function(){return l.notFound},permanentRedirect:function(){return l.permanentRedirect},redirect:function(){return l.redirect},useParams:function(){return h},usePathname:function(){return d},useRouter:function(){return f},useSearchParams:function(){return c},useSelectedLayoutSegment:function(){return m},useSelectedLayoutSegments:function(){return p},useServerInsertedHTML:function(){return u.useServerInsertedHTML}});let n=r(7577),i=r(2413),o=r(7008),a=r(2162),s=r(8071),l=r(7375),u=r(3347);function c(){let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new l.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(6136);e("useSearchParams()")}return t}function d(){return(0,n.useContext)(o.PathnameContext)}function f(){let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function h(){return(0,n.useContext)(o.PathParamsContext)}function p(e){void 0===e&&(e="children");let t=(0,n.useContext)(i.LayoutRouterContext);return t?function e(t,r,n,i){let o;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)o=t[1][r];else{var l;let e=t[1];o=null!=(l=e.children)?l:Object.values(e)[0]}if(!o)return i;let u=o[0],c=(0,a.getSegmentValue)(u);return!c||c.startsWith(s.PAGE_SEGMENT_KEY)?i:(i.push(c),e(o,r,!1,i))}(t.tree,e):null}function m(e){void 0===e&&(e="children");let t=p(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===s.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7375:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(2747),i=r(706);class o extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new o}delete(){throw new o}set(){throw new o}sort(){throw new o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1868:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return c}});let n=r(8374),i=r(326),o=n._(r(7577)),a=r(7389),s=r(706);r(576);let l=r(2413);class u extends o.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,s.isNotFoundError)(e))return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound]}):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function c(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:s}=e,c=(0,a.usePathname)(),d=(0,o.useContext)(l.MissingSlotContext);return t?(0,i.jsx)(u,{pathname:c,notFound:t,notFoundStyles:r,asNotFound:n,missingSlots:d,children:s}):(0,i.jsx)(i.Fragment,{children:s})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},706:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return i},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7815:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(8285),i=r(8817);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r;let i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6265:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectBoundary:function(){return c},RedirectErrorBoundary:function(){return u}});let n=r(8374),i=r(326),o=n._(r(7577)),a=r(7389),s=r(2747);function l(e){let{redirect:t,reset:r,redirectType:n}=e,i=(0,a.useRouter)();return(0,o.useEffect)(()=>{o.default.startTransition(()=>{n===s.RedirectType.push?i.push(t,{}):i.replace(t,{}),r()})},[t,n,r,i]),null}class u extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isRedirectError)(e))return{redirect:(0,s.getURLFromRedirectError)(e),redirectType:(0,s.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,i.jsx)(l,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function c(e){let{children:t}=e,r=(0,a.useRouter)();return(0,i.jsx)(u,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8778:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2747:(e,t,r)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return f},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return u}});let i=r(4580),o=r(2934),a=r(8778),s="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let o=i.requestAsyncStorage.getStore();return o&&(n.mutableCookies=o.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,i]=e.digest.split(";",4),o=Number(i);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(o)&&o in a.RedirectStatusCode}function f(e){return d(e)?e.digest.split(";",3)[2]:null}function h(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4759:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(8374),i=r(326),o=n._(r(7577)),a=r(2413);function s(){let e=(0,o.useContext)(a.TemplateContext);return(0,i.jsx)(i.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9894:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(114),i=r(9056);function o(e,t,r,o){let[a,s,l]=r.slice(-3);if(null===s)return!1;if(3===r.length){let r=s[2],i=s[3];t.loading=i,t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,o)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,i.fillCacheWithNewSubTreeData)(t,e,r,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5166:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,s){let l;let[u,c,d,f,h]=r;if(1===t.length){let e=a(r,n,t);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,s),e}let[p,m]=t;if(!(0,i.matchSegment)(p,u))return null;if(2===t.length)l=a(c[m],n,t);else if(null===(l=e(t.slice(2),c[m],n,s)))return null;let y=[t[0],{...c,[m]:l},d,f];return h&&(y[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(y,s),y}}});let n=r(8071),i=r(455),o=r(4158);function a(e,t,r){let[o,s]=e,[l,u]=t;if(l===n.DEFAULT_SEGMENT_KEY&&o!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(o,l)){let t={};for(let e in s)void 0!==u[e]?t[e]=a(s[e],u[e],r):t[e]=s[e];for(let e in u)t[e]||(t[e]=u[e]);let n=[o,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2895:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,s]=i,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a),c=t.parallelRoutes.get(a);c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c));let d=null==u?void 0:u.get(l),f=c.get(l);if(o){f&&f.lazyData&&f!==d||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}if(!f||!d){f||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved,loading:f.loading},c.set(l,f)),e(f,d,i.slice(2))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3648:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u}});let n=r(7356),i=r(8071),o=r(455),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),f=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var h;return null!=(h=u(r))?h:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7584:(e,t)=>{function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6199:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return u}});let n=r(7584),i=r(114),o=r(3648),a=r(9373),s=r(7767),l=r(4158);function u(e){var t;let{buildId:r,initialTree:u,initialSeedData:c,urlParts:d,initialParallelRoutes:f,location:h,initialHead:p,couldBeIntercepted:m}=e,y=d.join("/"),g=!h,v={lazyData:null,rsc:c[2],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:g?new Map:f,lazyDataResolved:!1,loading:c[3]},b=h?(0,n.createHrefFromUrl)(h):y;(0,l.addRefreshMarkerToActiveParallelSegments)(u,b);let x=new Map;(null===f||0===f.size)&&(0,i.fillLazyItemsTillLeafWithHead)(v,void 0,u,c,p);let P={buildId:r,tree:u,cache:v,prefetchCache:x,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:b,nextUrl:null!=(t=(0,o.extractPathFromFlightRouterState)(u)||(null==h?void 0:h.pathname))?t:null};if(h){let e=new URL(""+h.pathname+h.search,h.origin),t=[["",u,null,null]];(0,a.createPrefetchCacheEntryForInitialLoad)({url:e,kind:s.PrefetchKind.AUTO,data:[t,void 0,!1,m],tree:P.tree,prefetchCache:P.prefetchCache,nextUrl:P.nextUrl})}return P}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return i}});let n=r(8071);function i(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9009:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(5138),i=r(2994),o=r(5424),a=r(7767),s=r(2165),{createFromFetch:l}=r(6493);function u(e){return[(0,i.urlToUrlWithoutFlightMarker)(e).toString(),void 0,!1,!1]}async function c(e,t,r,c,d){let f={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===a.PrefetchKind.AUTO&&(f[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(f[n.NEXT_URL]=r);let h=(0,s.hexHash)([f[n.NEXT_ROUTER_PREFETCH_HEADER]||"0",f[n.NEXT_ROUTER_STATE_TREE],f[n.NEXT_URL]].join(","));try{var p;let t=new URL(e);t.pathname.endsWith("/")?t.pathname+="index.txt":t.pathname+=".txt",t.searchParams.set(n.NEXT_RSC_UNION_QUERY,h);let r=await fetch(t,{credentials:"same-origin",headers:f}),a=(0,i.urlToUrlWithoutFlightMarker)(r.url),s=r.redirected?a:void 0,d=r.headers.get("content-type")||"",m=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER),y=!!(null==(p=r.headers.get("vary"))?void 0:p.includes(n.NEXT_URL)),g=d===n.RSC_CONTENT_TYPE_HEADER;if(g||(g=d.startsWith("text/plain")),!g||!r.ok)return e.hash&&(a.hash=e.hash),u(a.toString());let[v,b]=await l(Promise.resolve(r),{callServer:o.callServer});if(c!==v)return u(r.url);return[b,s,m,y]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0,!1,!1]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9056:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,a,s){let l=a.length<=5,[u,c]=a,d=(0,o.createRouterCacheKey)(c),f=r.parallelRoutes.get(u);if(!f)return;let h=t.parallelRoutes.get(u);h&&h!==f||(h=new Map(f),t.parallelRoutes.set(u,h));let p=f.get(d),m=h.get(d);if(l){if(!m||!m.lazyData||m===p){let e=a[3];m={lazyData:null,rsc:e[2],prefetchRsc:null,head:null,prefetchHead:null,loading:e[3],parallelRoutes:p?new Map(p.parallelRoutes):new Map,lazyDataResolved:!1},p&&(0,n.invalidateCacheByRouterState)(m,p,a[2]),(0,i.fillLazyItemsTillLeafWithHead)(m,p,a[2],e,a[4],s),h.set(d,m)}return}m&&p&&(m===p&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),lazyDataResolved:!1,loading:m.loading},h.set(d,m)),e(m,p,a.slice(2),s))}}});let n=r(2498),i=r(114),o=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},114:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,o,a,s,l){if(0===Object.keys(o[1]).length){t.head=s;return}for(let u in o[1]){let c;let d=o[1][u],f=d[0],h=(0,n.createRouterCacheKey)(f),p=null!==a&&void 0!==a[1][u]?a[1][u]:null;if(r){let n=r.parallelRoutes.get(u);if(n){let r;let o=(null==l?void 0:l.kind)==="auto"&&l.status===i.PrefetchCacheEntryStatus.reusable,a=new Map(n),c=a.get(h);r=null!==p?{lazyData:null,rsc:p[2],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1}:o&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),lazyDataResolved:c.lazyDataResolved,loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1,loading:null},a.set(h,r),e(r,c,d,p||null,s,l),t.parallelRoutes.set(u,a);continue}}if(null!==p){let e=p[2],t=p[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};let m=t.parallelRoutes.get(u);m?m.set(h,c):t.parallelRoutes.set(u,new Map([[h,c]])),e(c,void 0,d,p,s,l)}}}});let n=r(9886),i=r(7767);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7252:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(3648);function i(e){return void 0!==e}function o(e,t){var r,o,a;let s=null==(o=t.shouldScroll)||o,l=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?l=r:l||(l=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#",1)[0]),hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5652:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(941);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3193:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,s]=i,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a);if(!u)return;let c=t.parallelRoutes.get(a);if(c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c)),o){c.delete(l);return}let d=u.get(l),f=c.get(l);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved},c.set(l,f)),e(f,d,i.slice(2)))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2498:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(9886);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3772:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8831:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return u},listenForDynamicRequest:function(){return s},updateCacheNodeOnNavigation:function(){return function e(t,r,s,u,c){let d=r[1],f=s[1],h=u[1],p=t.parallelRoutes,m=new Map(p),y={},g=null;for(let t in f){let r;let s=f[t],u=d[t],v=p.get(t),b=h[t],x=s[0],P=(0,o.createRouterCacheKey)(x),_=void 0!==u?u[0]:void 0,w=void 0!==v?v.get(P):void 0;if(null!==(r=x===n.PAGE_SEGMENT_KEY?a(s,void 0!==b?b:null,c):x===n.DEFAULT_SEGMENT_KEY?void 0!==u?{route:u,node:null,children:null}:a(s,void 0!==b?b:null,c):void 0!==_&&(0,i.matchSegment)(x,_)&&void 0!==w&&void 0!==u?null!=b?e(w,u,s,b,c):function(e){let t=l(e,null,null);return{route:e,node:t,children:null}}(s):a(s,void 0!==b?b:null,c))){null===g&&(g=new Map),g.set(t,r);let e=r.node;if(null!==e){let r=new Map(v);r.set(P,e),m.set(t,r)}y[t]=r.route}else y[t]=s}if(null===g)return null;let v={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:m,lazyDataResolved:!1};return{route:function(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}(s,y),node:v,children:g}}},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=f(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:null,prefetchRsc:l?t.prefetchRsc:null,loading:l?t.loading:null,parallelRoutes:a,lazyDataResolved:!1}}}});let n=r(8071),i=r(455),o=r(9886);function a(e,t,r){let n=l(e,t,r);return{route:e,node:n,children:null}}function s(e,t){t.then(t=>{for(let r of t[0]){let t=r.slice(0,-3),n=r[r.length-3],a=r[r.length-2],s=r[r.length-1];"string"!=typeof t&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}(function e(t,r,n,a){let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],d=a[1],h=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=d[t],f=h.get(t),p=r[0],m=(0,o.createRouterCacheKey)(p),y=void 0!==f?f.get(m):void 0;void 0!==y&&(void 0!==n&&(0,i.matchSegment)(p,n[0])&&null!=a?e(y,r,n,a,s):c(r,y,null))}let p=t.rsc,m=a[2];null===p?t.rsc=m:f(p)&&p.resolve(m);let y=t.head;f(y)&&y.resolve(s)}(l,t.route,r,n,a),t.node=null);return}let u=r[1],d=n[1];for(let t in r){let r=u[t],n=d[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}})(s,r,n,a)}(e,t,n,a,s)}u(e,null)},t=>{u(e,t)})}function l(e,t,r){let n=e[1],i=null!==t?t[1]:null,a=new Map;for(let e in n){let t=n[e],s=null!==i?i[e]:null,u=t[0],c=(0,o.createRouterCacheKey)(u),d=l(t,void 0===s?null:s,r),f=new Map;f.set(c,d),a.set(e,f)}let s=0===a.size,u=null!==t?t[2]:null,c=null!==t?t[3]:null;return{lazyData:null,parallelRoutes:a,prefetchRsc:void 0!==u?u:null,prefetchHead:s?r:null,loading:void 0!==c?c:null,rsc:h(),head:s?h():null,lazyDataResolved:!1}}function u(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)c(e.route,r,t);else for(let e of n.values())u(e,t);e.node=null}function c(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&c(t,u,r)}let a=t.rsc;f(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;f(s)&&s.resolve(null)}let d=Symbol();function f(e){return e&&e.tag===d}function h(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=d,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9373:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrefetchCacheEntryForInitialLoad:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(7584),i=r(9009),o=r(7767),a=r(1156);function s(e,t){let r=(0,n.createHrefFromUrl)(e,!1);return t?t+"%"+r:r}function l(e){let t,{url:r,nextUrl:n,tree:i,buildId:a,prefetchCache:l,kind:u}=e,d=s(r,n),f=l.get(d);if(f)t=f;else{let e=s(r),n=l.get(e);n&&(t=n)}return t?(t.status=p(t),t.kind!==o.PrefetchKind.FULL&&u===o.PrefetchKind.FULL)?c({tree:i,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:null!=u?u:o.PrefetchKind.TEMPORARY}):(u&&t.kind===o.PrefetchKind.TEMPORARY&&(t.kind=u),t):c({tree:i,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:u||o.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:i,kind:a,data:l}=e,[,,,u]=l,c=u?s(i,t):s(i),d={treeAtTimeOfPrefetch:r,data:Promise.resolve(l),kind:a,prefetchTime:Date.now(),lastUsedTime:Date.now(),key:c,status:o.PrefetchCacheEntryStatus.fresh};return n.set(c,d),d}function c(e){let{url:t,kind:r,tree:n,nextUrl:l,buildId:u,prefetchCache:c}=e,d=s(t),f=a.prefetchQueue.enqueue(()=>(0,i.fetchServerResponse)(t,n,l,u,r).then(e=>{let[,,,r]=e;return r&&function(e){let{url:t,nextUrl:r,prefetchCache:n}=e,i=s(t),o=n.get(i);if(!o)return;let a=s(t,r);n.set(a,o),n.delete(i)}({url:t,nextUrl:l,prefetchCache:c}),e})),h={treeAtTimeOfPrefetch:n,data:f,kind:r,prefetchTime:Date.now(),lastUsedTime:null,key:d,status:o.PrefetchCacheEntryStatus.fresh};return c.set(d,h),h}function d(e){for(let[t,r]of e)p(r)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("30"),h=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+f?n?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:"auto"===t&&Date.now()<r+h?o.PrefetchCacheEntryStatus.stale:"full"===t&&Date.now()<r+h?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5703:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9009),r(7584),r(5166),r(3772),r(941),r(7252),r(9894),r(2994),r(5652),r(5262);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2492:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(9886);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];for(let o in r){let[a,s]=r[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2162:(e,t)=>{function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5262:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function e(t){let[r,i]=t;if(Array.isArray(r)&&("di"===r[2]||"ci"===r[2])||"string"==typeof r&&(0,n.isInterceptionRouteAppPath)(r))return!0;if(i){for(let t in i)if(e(i[t]))return!0}return!1}}});let n=r(7356);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},941:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return y},navigateReducer:function(){return v}}),r(9009);let n=r(7584),i=r(3193),o=r(5166),a=r(4614),s=r(3772),l=r(7767),u=r(7252),c=r(9894),d=r(1156),f=r(2994),h=r(8071),p=(r(8831),r(9373)),m=r(2895);function y(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,u.handleMutable)(e,t)}function g(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of g(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}let v=function(e,t){let{url:r,isExternalUrl:v,navigateType:b,shouldScroll:x}=t,P={},{hash:_}=r,w=(0,n.createHrefFromUrl)(r),R="push"===b;if((0,p.prunePrefetchCache)(e.prefetchCache),P.preserveCustomHistoryState=!1,v)return y(e,P,r.toString(),R);let E=(0,p.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,tree:e.tree,buildId:e.buildId,prefetchCache:e.prefetchCache}),{treeAtTimeOfPrefetch:S,data:T}=E;return d.prefetchQueue.bump(T),T.then(t=>{let[r,d]=t,p=!1;if(E.lastUsedTime||(E.lastUsedTime=Date.now(),p=!0),"string"==typeof r)return y(e,P,r,R);if(document.getElementById("__next-page-redirect"))return y(e,P,w,R);let v=e.tree,b=e.cache,T=[];for(let t of r){let r=t.slice(0,-4),n=t.slice(-3)[0],u=["",...r],d=(0,o.applyRouterStatePatchToTree)(u,v,n,w);if(null===d&&(d=(0,o.applyRouterStatePatchToTree)(u,S,n,w)),null!==d){if((0,s.isNavigatingToNewRootLayout)(v,d))return y(e,P,w,R);let o=(0,f.createEmptyCacheNode)(),x=!1;for(let e of(E.status!==l.PrefetchCacheEntryStatus.stale||p?x=(0,c.applyFlightData)(b,o,t,E):(x=function(e,t,r,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),g(n).map(e=>[...r,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(o,b,r,n),E.lastUsedTime=Date.now()),(0,a.shouldHardNavigate)(u,v)?(o.rsc=b.rsc,o.prefetchRsc=b.prefetchRsc,(0,i.invalidateCacheBelowFlightSegmentPath)(o,b,r),P.cache=o):x&&(P.cache=o,b=o),v=d,g(n))){let t=[...r,...e];t[t.length-1]!==h.DEFAULT_SEGMENT_KEY&&T.push(t)}}}return P.patchedTree=v,P.canonicalUrl=d?(0,n.createHrefFromUrl)(d):w,P.pendingPush=R,P.scrollableSegments=T,P.hashFragment=_,P.shouldScroll=x,(0,u.handleMutable)(e,P)},()=>e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1156:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let n=r(5138),i=r(7815),o=r(9373),a=new i.PromiseQueue(5);function s(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return r.searchParams.delete(n.NEXT_RSC_UNION_QUERY),(0,o.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,buildId:e.buildId}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9809:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9009),i=r(7584),o=r(5166),a=r(3772),s=r(941),l=r(7252),u=r(114),c=r(2994),d=r(5652),f=r(5262),h=r(4158);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return g.lazyData=(0,n.fetchServerResponse)(new URL(m,r),[y[0],y[1],y[2],"refetch"],v?e.nextUrl:null,e.buildId),g.lazyData.then(async r=>{let[n,c]=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){if(3!==r.length)return console.log("REFRESH FAILED"),e;let[n]=r,l=(0,o.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===l)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(y,l))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let f=c?(0,i.createHrefFromUrl)(c):void 0;c&&(p.canonicalUrl=f);let[b,x]=r.slice(-2);if(null!==b){let e=b[2];g.rsc=e,g.prefetchRsc=null,(0,u.fillLazyItemsTillLeafWithHead)(g,void 0,n,b,x),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({state:e,updatedTree:l,updatedCache:g,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=g,p.patchedTree=l,p.canonicalUrl=m,y=l}return(0,l.handleMutable)(e,p)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5608:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(7584),i=r(3648);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{buildId:e.buildId,canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:o.pathname}}r(8831),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5240:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return b}});let n=r(5424),i=r(5138),o=r(3486),a=r(7584),s=r(941),l=r(5166),u=r(3772),c=r(7252),d=r(114),f=r(2994),h=r(5262),p=r(5652),m=r(4158),{createFromFetch:y,encodeReply:g}=r(6493);async function v(e,t,r){let a,{actionId:s,actionArgs:l}=r,u=await g(l),c=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION]:s,[i.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[i.NEXT_URL]:t}:{}},body:u}),d=c.headers.get("x-action-redirect");try{let e=JSON.parse(c.headers.get("x-action-revalidated")||"[[],0,0]");a={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){a={paths:[],tag:!1,cookie:!1}}let f=d?new URL((0,o.addBasePath)(d),new URL(e.canonicalUrl,window.location.href)):void 0;if(c.headers.get("content-type")===i.RSC_CONTENT_TYPE_HEADER){let e=await y(Promise.resolve(c),{callServer:n.callServer});if(d){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:f,revalidatedParts:a}}let[t,[,r]]=null!=e?e:[];return{actionResult:t,actionFlightData:r,redirectLocation:f,revalidatedParts:a}}return{redirectLocation:f,revalidatedParts:a}}function b(e,t){let{resolve:r,reject:n}=t,i={},o=e.canonicalUrl,y=e.tree;i.preserveCustomHistoryState=!1;let g=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return i.inFlightServerAction=v(e,g,t),i.inFlightServerAction.then(async n=>{let{actionResult:h,actionFlightData:v,redirectLocation:b}=n;if(b&&(e.pushRef.pendingPush=!0,i.pendingPush=!0),!v)return(r(h),b)?(0,s.handleExternalUrl)(e,i,b.href,e.pushRef.pendingPush):e;if("string"==typeof v)return(0,s.handleExternalUrl)(e,i,v,e.pushRef.pendingPush);if(i.inFlightServerAction=null,b){let e=(0,a.createHrefFromUrl)(b,!1);i.canonicalUrl=e}for(let r of v){if(3!==r.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[n]=r,c=(0,l.applyRouterStatePatchToTree)([""],y,n,b?(0,a.createHrefFromUrl)(b):e.canonicalUrl);if(null===c)return(0,p.handleSegmentMismatch)(e,t,n);if((0,u.isNavigatingToNewRootLayout)(y,c))return(0,s.handleExternalUrl)(e,i,o,e.pushRef.pendingPush);let[h,v]=r.slice(-2),x=null!==h?h[2]:null;if(null!==x){let t=(0,f.createEmptyCacheNode)();t.rsc=x,t.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(t,void 0,n,h,v),await (0,m.refreshInactiveParallelSegments)({state:e,updatedTree:c,updatedCache:t,includeNextUrl:!!g,canonicalUrl:i.canonicalUrl||e.canonicalUrl}),i.cache=t,i.prefetchCache=new Map}i.patchedTree=c,y=c}return r(h),(0,c.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4025:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let n=r(7584),i=r(5166),o=r(3772),a=r(941),s=r(9894),l=r(7252),u=r(2994),c=r(5652);function d(e,t){let{serverResponse:r}=t,[d,f]=r,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof d)return(0,a.handleExternalUrl)(e,h,d,e.pushRef.pendingPush);let p=e.tree,m=e.cache;for(let r of d){let l=r.slice(0,-4),[d]=r.slice(-3,-2),y=(0,i.applyRouterStatePatchToTree)(["",...l],p,d,e.canonicalUrl);if(null===y)return(0,c.handleSegmentMismatch)(e,t,d);if((0,o.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=f?(0,n.createHrefFromUrl)(f):void 0;g&&(h.canonicalUrl=g);let v=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(m,v,r),h.patchedTree=y,h.cache=v,m=v,p=y}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4158:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,a]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),i)e(i[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(9894),i=r(9009),o=r(8071);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{state:t,updatedTree:r,updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u=r,canonicalUrl:c}=e,[,d,f,h]=r,p=[];if(f&&f!==c&&"refresh"===h&&!l.has(f)){l.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),[u[0],u[1],u[2],"refetch"],a?t.nextUrl:null,t.buildId).then(e=>{let t=e[0];if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(o,o,e)});p.push(e)}for(let e in d){let r=s({state:t,updatedTree:d[e],updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u,canonicalUrl:c});p.push(r)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7767:(e,t)=>{var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return u},ACTION_NAVIGATE:function(){return o},ACTION_PREFETCH:function(){return l},ACTION_REFRESH:function(){return i},ACTION_RESTORE:function(){return a},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return s},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let i="refresh",o="navigate",a="restore",s="server-patch",l="prefetch",u="fast-refresh",c="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3860:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(7767),r(941),r(4025),r(5608),r(9809),r(1156),r(5703),r(5240);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,o]=r,[a,s]=t;return(0,n.matchSegment)(a,i)?!(t.length<=2)&&e(t.slice(2),o[s]):!!Array.isArray(a)}}});let n=r(455);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(5869),i=r(2846),o=r(2255);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),o.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,i.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6488:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return i}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function i(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9519:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unresolvedThenable",{enumerable:!0,get:function(){return r}});let r={then:()=>{}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7326:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducerWithReduxDevtools:function(){return s},useUnwrapState:function(){return a}});let n=r(8374)._(r(7577)),i=r(7767);function o(e){if(e instanceof Map){let t={};for(let[r,n]of e.entries()){if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n._bundlerConfig){t[r]="FlightData";continue}}t[r]=o(n)}return t}if("object"==typeof e&&null!==e){let t={};for(let r in e){let n=e[r];if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n.hasOwnProperty("_bundlerConfig")){t[r]="FlightData";continue}}t[r]=o(n)}return t}return Array.isArray(e)?e.map(o):e}function a(e){return(0,i.isThenable)(e)?(0,n.use)(e):e}r(3879);let s=function(e){return[e,()=>{},()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9683:(e,t,r)=>{function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(3658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7929:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(4655);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9404:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return v}});let n=r(1174),i=r(326),o=n._(r(7577)),a=r(5619),s=r(944),l=r(3071),u=r(1348),c=r(3416),d=r(131),f=r(2413),h=r(9408),p=r(9683),m=r(3486),y=r(7767);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let v=o.default.forwardRef(function(e,t){let r,n;let{href:l,as:v,children:b,prefetch:x=null,passHref:P,replace:_,shallow:w,scroll:R,locale:E,onClick:S,onMouseEnter:T,onTouchStart:j,legacyBehavior:O=!1,...M}=e;r=b,O&&("string"==typeof r||"number"==typeof r)&&(r=(0,i.jsx)("a",{children:r}));let A=o.default.useContext(d.RouterContext),C=o.default.useContext(f.AppRouterContext),N=null!=A?A:C,D=!A,k=!1!==x,I=null===x?y.PrefetchKind.AUTO:y.PrefetchKind.FULL,{href:L,as:F}=o.default.useMemo(()=>{if(!A){let e=g(l);return{href:e,as:v?g(v):e}}let[e,t]=(0,a.resolveHref)(A,l,!0);return{href:e,as:v?(0,a.resolveHref)(A,v):t||e}},[A,l,v]),U=o.default.useRef(L),V=o.default.useRef(F);O&&(n=o.default.Children.only(r));let B=O?n&&"object"==typeof n&&n.ref:t,[W,z,H]=(0,h.useIntersection)({rootMargin:"200px"}),$=o.default.useCallback(e=>{(V.current!==F||U.current!==L)&&(H(),V.current=F,U.current=L),W(e),B&&("function"==typeof B?B(e):"object"==typeof B&&(B.current=e))},[F,B,L,H,W]);o.default.useEffect(()=>{},[F,L,z,E,k,null==A?void 0:A.locale,N,D,I]);let G={ref:$,onClick(e){O||"function"!=typeof S||S(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&!e.defaultPrevented&&function(e,t,r,n,i,a,l,u,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,s.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==l||l;"beforePopState"in t?t[i?"replace":"push"](r,n,{shallow:a,locale:u,scroll:e}):t[i?"replace":"push"](n||r,{scroll:e})};c?o.default.startTransition(f):f()}(e,N,L,F,_,w,R,E,D)},onMouseEnter(e){O||"function"!=typeof T||T(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){O||"function"!=typeof j||j(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,u.isAbsoluteUrl)(F))G.href=F;else if(!O||P||"a"===n.type&&!("href"in n.props)){let e=void 0!==E?E:null==A?void 0:A.locale,t=(null==A?void 0:A.isLocaleDomain)&&(0,p.getDomainLocale)(F,e,null==A?void 0:A.locales,null==A?void 0:A.domainLocales);G.href=t||(0,m.addBasePath)((0,c.addLocale)(F,e,null==A?void 0:A.defaultLocale))}return O?o.default.cloneElement(n,G):(0,i.jsx)("a",{...M,...G,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3658:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(3236),i=r(3067),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return/\.[^/]+\/?$/.test(t)?""+(0,n.removeTrailingSlash)(t)+r+o:t.endsWith("/")?""+t+r+o:t+"/"+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4237:(e,t,r)=>{function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(7929),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},956:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5619:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(2149),i=r(3071),o=r(757),a=r(1348),s=r(3658),l=r(944),u=r(4903),c=r(1394);function d(e,t,r){let d;let f="string"==typeof t?t:(0,i.formatWithValidation)(t),h=f.match(/^[a-zA-Z]{1,}:\/\//),p=h?f.slice(h[0].length):f;if((p.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(p);f=(h?h[0]:"")+t}if(!(0,l.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:s}=(0,c.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,i.formatWithValidation)({pathname:a,hash:e.hash,query:(0,o.omit)(r,s)}))}let a=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9408:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let n=r(7577),i=r(956),o="function"==typeof IntersectionObserver,a=new Map,s=[];function l(e){let{rootRef:t,rootMargin:r,disabled:l}=e,u=l||!o,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),h=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(o){if(u||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:i,elements:o}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=s.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let i=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=i.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:i},s.push(r),a.set(r,t),t}(r);return o.set(e,t),i.observe(e),function(){if(o.delete(e),i.unobserve(e),0===o.size){i.disconnect(),a.delete(n);let e=s.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&s.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,i.requestIdleCallback)(()=>d(!0));return()=>(0,i.cancelIdleCallback)(e)}},[u,r,t,c,f.current]),[h,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5633:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return l},APP_DIR_ALIAS:function(){return T},CACHE_ONE_YEAR:function(){return x},DOT_NEXT_ALIAS:function(){return E},ESLINT_DEFAULT_DIRS:function(){return $},GSP_NO_RETURNED_VALUE:function(){return U},GSSP_COMPONENT_MEMBER_ERROR:function(){return W},GSSP_NO_RETURNED_VALUE:function(){return V},INSTRUMENTATION_HOOK_FILENAME:function(){return w},MIDDLEWARE_FILENAME:function(){return P},MIDDLEWARE_LOCATION_REGEXP:function(){return _},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return b},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return p},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return h},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return y},NEXT_CACHE_TAG_MAX_LENGTH:function(){return g},NEXT_DATA_SUFFIX:function(){return u},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return c},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return z},PAGES_DIR_ALIAS:function(){return R},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return N},ROOT_DIR_ALIAS:function(){return S},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return C},RSC_ACTION_ENCRYPTION_ALIAS:function(){return A},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return O},RSC_MOD_REF_PROXY_ALIAS:function(){return j},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SUFFIX:function(){return s},SERVER_PROPS_EXPORT_ERROR:function(){return F},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return k},SERVER_PROPS_SSG_CONFLICT:function(){return I},SERVER_RUNTIME:function(){return G},SSG_FALLBACK_EXPORT_ERROR:function(){return H},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return D},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return L},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return B},WEBPACK_LAYERS:function(){return X},WEBPACK_RESOURCE_QUERIES:function(){return Y}});let r="nxtP",n="nxtI",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=".prefetch.rsc",s=".rsc",l=".action",u=".json",c=".meta",d=".body",f="x-next-cache-tags",h="x-next-cache-soft-tags",p="x-next-revalidated-tags",m="x-next-revalidate-tag-token",y=128,g=256,v=1024,b="_N_T_",x=31536e3,P="middleware",_=`(?:src/)?${P}`,w="instrumentation",R="private-next-pages",E="private-dot-next",S="private-next-root-dir",T="private-next-app-dir",j="private-next-rsc-mod-ref-proxy",O="private-next-rsc-action-validate",M="private-next-rsc-server-reference",A="private-next-rsc-action-encryption",C="private-next-rsc-action-client-wrapper",N="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",D="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",k="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",I="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",L="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",F="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",U="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",V="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",B="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",W="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",z='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',H="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",$=["app","pages","components","lib","src"],G={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},K={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},X={...K,GROUP:{serverOnly:[K.reactServerComponents,K.actionBrowser,K.appMetadataRoute,K.appRouteHandler,K.instrument],clientOnly:[K.serverSideRendering,K.appPagesBrowser],nonClientServerTarget:[K.middleware,K.api],app:[K.reactServerComponents,K.actionBrowser,K.appMetadataRoute,K.appRouteHandler,K.serverSideRendering,K.appPagesBrowser,K.shared,K.instrument]}},Y={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},6401:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPathname:function(){return n},isFullStringUrl:function(){return i},parseUrl:function(){return o}});let r="http://n";function n(e){return new URL(e,r).pathname}function i(e){return/https?:\/\//.test(e)}function o(e){let t;try{t=new URL(e,r)}catch{}return t}},2846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return d},createPostponedAbortSignal:function(){return g},createPrerenderState:function(){return l},formatDynamicAPIAccesses:function(){return m},markCurrentScopeAsDynamic:function(){return u},trackDynamicDataAccessed:function(){return c},trackDynamicFetch:function(){return f},usedDynamicAPIs:function(){return p}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7577)),i=r(442),o=r(6488),a=r(6401),s="function"==typeof n.default.unstable_postpone;function l(e){return{isDebugSkeleton:e,dynamicAccesses:[]}}function u(e,t){let r=(0,a.getPathname)(e.urlPathname);if(!e.isUnstableCacheCallback){if(e.dynamicShouldError)throw new o.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new i.DynamicServerError(`Route ${r} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}}function c(e,t){let r=(0,a.getPathname)(e.urlPathname);if(e.isUnstableCacheCallback)throw Error(`Route ${r} used "${t}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${t}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if(e.dynamicShouldError)throw new o.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new i.DynamicServerError(`Route ${r} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}function d({reason:e,prerenderState:t,pathname:r}){h(t,e,r)}function f(e,t){e.prerenderState&&h(e.prerenderState,t,e.urlPathname)}function h(e,t,r){y();let i=`Route ${r} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;e.dynamicAccesses.push({stack:e.isDebugSkeleton?Error().stack:void 0,expression:t}),n.default.unstable_postpone(i)}function p(e){return e.dynamicAccesses.length>0}function m(e){return e.dynamicAccesses.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function y(){if(!s)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")}function g(e){y();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}},2357:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return i}});let n=r(7356);function i(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:t?"catchall-intercepted":"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:t?"dynamic-intercepted":"dynamic",param:e.slice(1,-1)}:null}},7356:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(2862),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?`/${o}`:t+"/"+o;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);o=a.slice(0,-2).concat(o).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:o}}},1616:(e,t,r)=>{e.exports=r(399)},2413:(e,t,r)=>{e.exports=r(1616).vendored.contexts.AppRouterContext},7008:(e,t,r)=>{e.exports=r(1616).vendored.contexts.HooksClientContext},131:(e,t,r)=>{e.exports=r(1616).vendored.contexts.RouterContext},3347:(e,t,r)=>{e.exports=r(1616).vendored.contexts.ServerInsertedHtml},962:(e,t,r)=>{e.exports=r(1616).vendored["react-ssr"].ReactDOM},326:(e,t,r)=>{e.exports=r(1616).vendored["react-ssr"].ReactJsxRuntime},6493:(e,t,r)=>{e.exports=r(1616).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},7577:(e,t,r)=>{e.exports=r(1616).vendored["react-ssr"].React},2255:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2451:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},2165:(e,t)=>{function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},4129:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},6058:(e,t)=>{function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},3879:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ActionQueueContext:function(){return s},createMutableActionQueue:function(){return c}});let n=r(8374),i=r(7767),o=r(3860),a=n._(r(7577)),s=a.default.createContext(null);function l(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?u({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:i.ACTION_REFRESH,origin:window.location.origin},t)))}async function u(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;if(!o)throw Error("Invariant: Router state not initialized");t.pending=r;let a=r.payload,s=t.action(o,a);function u(e){r.discarded||(t.state=e,t.devToolsInstance&&t.devToolsInstance.send(a,e),l(t,n),r.resolve(e))}(0,i.isThenable)(s)?s.then(u,e=>{l(t,n),r.reject(e)}):u(s)}function c(){let e={state:null,dispatch:(t,r)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==i.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=o,u({actionQueue:e,action:o,setState:r})):t.type===i.ACTION_NAVIGATE||t.type===i.ACTION_RESTORE?(e.pending.discarded=!0,e.last=o,e.pending.payload.type===i.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),u({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(e,t,r),action:async(e,t)=>{if(null===e)throw Error("Invariant: Router state not initialized");return(0,o.reducer)(e,t)},pending:null,last:null};return e}},8974:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(3067);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},2862:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(6058),i=r(8071);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},3071:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(8374)._(r(2149)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},9976:(e,t)=>{function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},4903:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let n=r(4712),i=r(5541)},1394:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return o}});let n=r(9966),i=r(7249);function o(e,t,r){let o="",a=(0,i.getRouteRegex)(e),s=a.groups,l=(t!==e?(0,n.getRouteMatcher)(a)(t):"")||r;o=e;let u=Object.keys(s);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=s[e],i="["+(r?"...":"")+e+"]";return n&&(i=(t?"":"/")+"["+i+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(o=o.replace(i,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(o=""),{params:u,result:o}}},2148:(e,t)=>{function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},5541:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(7356),i=/\/\[[^/]+?\](?=\/|$)/;function o(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),i.test(e)}},944:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=r(1348),i=r(7929);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},757:(e,t)=>{function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},3067:(e,t)=>{function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},4655:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(3067);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2149:(e,t)=>{function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,i]=e;Array.isArray(i)?i.forEach(e=>t.append(r,n(e))):t.set(r,n(i))}),t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},3236:(e,t)=>{function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},9966:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(1348);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=i[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>o(e)):t.repeat?[o(n)]:o(n))}),a}}},7249:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return s}});let n=r(5633),i=r(7356),o=r(2451),a=r(3236);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=i.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:i,repeat:l}=s(a[1]);return r[e]={pos:n++,repeat:l,optional:i},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:i}=s(a[1]);return r[e]={pos:n++,repeat:t,optional:i},t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function u(e){let{parameterizedRoute:t,groups:r}=l(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:i,keyPrefix:a}=e,{key:l,optional:u,repeat:c}=s(n),d=l.replace(/\W/g,"");a&&(d=""+a+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),a?i[d]=""+a+l:i[d]=l;let h=t?(0,o.escapeStringRegexp)(t):"";return c?u?"(?:/"+h+"(?<"+d+">.+?))?":"/"+h+"(?<"+d+">.+?)":"/"+h+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let s=(0,a.removeTrailingSlash)(e).slice(1).split("/"),l=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:s.map(e=>{let r=i.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return c({getSafeRouteKey:l,interceptionMarker:r,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return a?c({getSafeRouteKey:l,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function f(e,t){let r=d(e,t);return{...u(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function h(e,t){let{parameterizedRoute:r}=l(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=d(e,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4712:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let r=i.slice(1,-1),a=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),a=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function o(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(a){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');o(this.optionalRestSlugName,r),this.optionalRestSlugName=r,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');o(this.restSlugName,r),this.restSlugName=r,i="[...]"}}else{if(a)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');o(this.slugName,r),this.slugName=r,i="[]"}}this.children.has(i)||this.children.set(i,new r),this.children.get(i)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},8071:(e,t)=>{function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return i},PAGE_SEGMENT_KEY:function(){return n},isGroupSegment:function(){return r}});let n="__PAGE__",i="__DEFAULT__"},1348:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},576:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},8570:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(1749).createClientModuleProxy},9943:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/app-router.js")},3144:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/client-page.js")},7922:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/error-boundary.js")},5106:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/layout-router.js")},525:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/not-found-boundary.js")},5866:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r(3370);let n=r(9510);r(1159);let i={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function o(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:"404: This page could not be found."}),(0,n.jsx)("div",{style:i.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:i.h1,children:"404"}),(0,n.jsx)("div",{style:i.desc,children:(0,n.jsx)("h2",{style:i.h2,children:"This page could not be found."})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4892:(e,t,r)=>{let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Workspace/auspex/auspex-website/node_modules/next/dist/client/components/render-from-template-context.js")},9181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(5869),i=r(6278),o=r(8238);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),o.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,i.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5231:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouter:function(){return i.default},ClientPageRoot:function(){return c.ClientPageRoot},LayoutRouter:function(){return o.default},NotFoundBoundary:function(){return h.NotFoundBoundary},Postpone:function(){return y.Postpone},RenderFromTemplateContext:function(){return a.default},actionAsyncStorage:function(){return u.actionAsyncStorage},createDynamicallyTrackedSearchParams:function(){return d.createDynamicallyTrackedSearchParams},createUntrackedSearchParams:function(){return d.createUntrackedSearchParams},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},decodeReply:function(){return n.decodeReply},patchFetch:function(){return x},preconnect:function(){return m.preconnect},preloadFont:function(){return m.preloadFont},preloadStyle:function(){return m.preloadStyle},renderToReadableStream:function(){return n.renderToReadableStream},requestAsyncStorage:function(){return l.requestAsyncStorage},serverHooks:function(){return f},staticGenerationAsyncStorage:function(){return s.staticGenerationAsyncStorage},taintObjectReference:function(){return g.taintObjectReference}});let n=r(1749),i=v(r(9943)),o=v(r(5106)),a=v(r(4892)),s=r(5869),l=r(4580),u=r(2934),c=r(3144),d=r(9181),f=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=b(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(4789)),h=r(525),p=r(670);r(7922);let m=r(135),y=r(9257),g=r(526);function v(e){return e&&e.__esModule?e:{default:e}}function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(b=function(e){return e?r:t})(e)}function x(){return(0,p.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:s.staticGenerationAsyncStorage})}},9257:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Postpone",{enumerable:!0,get:function(){return n.Postpone}});let n=r(6278)},135:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preconnect:function(){return a},preloadFont:function(){return o},preloadStyle:function(){return i}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7049));function i(e,t){let r={as:"style"};"string"==typeof t&&(r.crossOrigin=t),n.default.preload(e,r)}function o(e,t,r){let i={as:"font",type:t};"string"==typeof r&&(i.crossOrigin=r),n.default.preload(e,i)}function a(e,t){n.default.preconnect(e,"string"==typeof t?{crossOrigin:t}:void 0)}},526:(e,t,r)=>{function n(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return i},taintUniqueValue:function(){return o}}),r(1159);let i=n,o=n},7049:(e,t,r)=>{e.exports=r(3191).vendored["react-rsc"].ReactDOM},9510:(e,t,r)=>{e.exports=r(3191).vendored["react-rsc"].ReactJsxRuntime},1749:(e,t,r)=>{e.exports=r(3191).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},8238:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2561:(e,t,r)=>{r.d(t,{M:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},8051:(e,t,r)=>{r.d(t,{F:()=>o,e:()=>a});var n=r(7577);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},3095:(e,t,r)=>{r.d(t,{b:()=>a,k:()=>o});var n=r(7577),i=r(326);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let a=n.createContext(o),s=r.length;r=[...r,o];let l=t=>{let{scope:r,children:o,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},825:(e,t,r)=>{r.d(t,{I0:()=>g,XB:()=>f,fC:()=>y});var n,i=r(7577),o=r(2561),a=r(7335),s=r(8051),l=r(5049),u=r(326),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:g,onDismiss:v,...b}=e,x=i.useContext(d),[P,_]=i.useState(null),w=P?.ownerDocument??globalThis?.document,[,R]=i.useState({}),E=(0,s.e)(t,e=>_(e)),S=Array.from(x.layers),[T]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),j=S.indexOf(T),O=P?S.indexOf(P):-1,M=x.layersWithOutsidePointerEventsDisabled.size>0,A=O>=j,C=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));!A||r||(h?.(e),g?.(e),e.defaultPrevented||v?.())},w),N=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(y?.(e),g?.(e),e.defaultPrevented||v?.())},w);return function(e,t=globalThis?.document){let r=(0,l.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{O!==x.layers.size-1||(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},w),i.useEffect(()=>{if(P)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(n=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(P)),x.layers.add(P),p(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=n)}},[P,w,r,x]),i.useEffect(()=>()=>{P&&(x.layers.delete(P),x.layersWithOutsidePointerEventsDisabled.delete(P),p())},[P,x]),i.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.WV.div,{...b,ref:E,style:{pointerEvents:M?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,C.onPointerDownCapture)})});f.displayName="DismissableLayer";var h=i.forwardRef((e,t)=>{let r=i.useContext(d),n=i.useRef(null),o=(0,s.e)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.WV.div,{...e,ref:o})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,a.jH)(i,o):i.dispatchEvent(o)}h.displayName="DismissableLayerBranch";var y=f,g=h},3078:(e,t,r)=>{r.d(t,{h:()=>l});var n=r(7577),i=r(962),o=r(7335),a=r(5819),s=r(326),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.b)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?i.createPortal((0,s.jsx)(o.WV.div,{...l,ref:t}),d):null});l.displayName="Portal"},9815:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(7577),i=r(8051),o=r(5819),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[i,a]=n.useState(),l=n.useRef({}),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.b)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,i=s(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,o.b)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===i&&n&&(f("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(l.current=getComputedStyle(e)),a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,i.e)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},7335:(e,t,r)=>{r.d(t,{WV:()=>d,jH:()=>f});var n=r(7577),i=r(962),o=r(8051),a=r(326),s=n.forwardRef((e,t)=>{let{children:r,...i}=e,o=n.Children.toArray(r),s=o.find(c);if(s){let e=s.props.children,r=o.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(l,{...i,ref:t,children:r})});s.displayName="Slot";var l=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),a=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,o.F)(t,e):e),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});l.displayName="SlotClone";var u=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===u}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...i}=e,o=n?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function f(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4559:(e,t,r)=>{r.d(t,{aU:()=>ei,x8:()=>eo,dk:()=>en,zt:()=>J,fC:()=>et,Dx:()=>er,l_:()=>ee});var n=r(7577),i=r(962),o=r(2561),a=r(8051),s=r(3095),l=r(326),u=n.forwardRef((e,t)=>{let{children:r,...i}=e,o=n.Children.toArray(r),a=o.find(f);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(c,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,l.jsx)(c,{...i,ref:t,children:r})});u.displayName="Slot";var c=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,a.F)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});c.displayName="SlotClone";var d=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function f(e){return n.isValidElement(e)&&e.type===d}var h=r(825),p=r(3078),m=r(9815),y=r(7335),g=r(5049),v=r(2067),b=r(5819),x=r(6009),P="ToastProvider",[_,w,R]=function(e){let t=e+"CollectionProvider",[r,i]=(0,s.b)(t),[o,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),a=n.useRef(new Map).current;return(0,l.jsx)(o,{scope:t,itemMap:a,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),o=(0,a.e)(t,i.collectionRef);return(0,l.jsx)(u,{ref:o,children:n})});h.displayName=f;let p=e+"CollectionItemSlot",m="data-radix-collection-item",y=n.forwardRef((e,t)=>{let{scope:r,children:i,...o}=e,s=n.useRef(null),d=(0,a.e)(t,s),f=c(p,r);return n.useEffect(()=>(f.itemMap.set(s,{ref:s,...o}),()=>void f.itemMap.delete(s))),(0,l.jsx)(u,{[m]:"",ref:d,children:i})});return y.displayName=p,[{Provider:d,Slot:h,ItemSlot:y},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}("Toast"),[E,S]=(0,s.b)("Toast",[R]),[T,j]=E(P),O=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:a=50,children:s}=e,[u,c]=n.useState(null),[d,f]=n.useState(0),h=n.useRef(!1),p=n.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${P}\`. Expected non-empty \`string\`.`),(0,l.jsx)(_.Provider,{scope:t,children:(0,l.jsx)(T,{scope:t,label:r,duration:i,swipeDirection:o,swipeThreshold:a,toastCount:d,viewport:u,onViewportChange:c,onToastAdd:n.useCallback(()=>f(e=>e+1),[]),onToastRemove:n.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:p,children:s})})};O.displayName=P;var M="ToastViewport",A=["F8"],C="toast.viewportPause",N="toast.viewportResume",D=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=A,label:o="Notifications ({hotkey})",...s}=e,u=j(M,r),c=w(r),d=n.useRef(null),f=n.useRef(null),p=n.useRef(null),m=n.useRef(null),g=(0,a.e)(t,m,u.onViewportChange),v=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=u.toastCount>0;n.useEffect(()=>{let e=e=>{0!==i.length&&i.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{let e=d.current,t=m.current;if(b&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(C);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},o=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",o),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[b,u.isClosePausedRef]);let x=n.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){f.current?.focus();return}let i=x({tabbingDirection:n?"backwards":"forwards"}),o=i.findIndex(e=>e===r);Q(i.slice(o+1))?t.preventDefault():n?f.current?.focus():p.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,x]),(0,l.jsxs)(h.I0,{ref:d,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&(0,l.jsx)(I,{ref:f,onFocusFromOutsideViewport:()=>{Q(x({tabbingDirection:"forwards"}))}}),(0,l.jsx)(_.Slot,{scope:r,children:(0,l.jsx)(y.WV.ol,{tabIndex:-1,...s,ref:g})}),b&&(0,l.jsx)(I,{ref:p,onFocusFromOutsideViewport:()=>{Q(x({tabbingDirection:"backwards"}))}})]})});D.displayName=M;var k="ToastFocusProxy",I=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,o=j(k,r);return(0,l.jsx)(x.T,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;o.viewport?.contains(t)||n()}})});I.displayName=k;var L="Toast",F=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:a,...s}=e,[u=!0,c]=(0,v.T)({prop:n,defaultProp:i,onChange:a});return(0,l.jsx)(m.z,{present:r||u,children:(0,l.jsx)(B,{open:u,...s,ref:t,onClose:()=>c(!1),onPause:(0,g.W)(e.onPause),onResume:(0,g.W)(e.onResume),onSwipeStart:(0,o.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,o.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),c(!1)})})})});F.displayName=L;var[U,V]=E(L,{onClose(){}}),B=n.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:u,open:c,onClose:d,onEscapeKeyDown:f,onPause:p,onResume:m,onSwipeStart:v,onSwipeMove:b,onSwipeCancel:x,onSwipeEnd:P,...w}=e,R=j(L,r),[E,S]=n.useState(null),T=(0,a.e)(t,e=>S(e)),O=n.useRef(null),M=n.useRef(null),A=u||R.duration,D=n.useRef(0),k=n.useRef(A),I=n.useRef(0),{onToastAdd:F,onToastRemove:V}=R,B=(0,g.W)(()=>{E?.contains(document.activeElement)&&R.viewport?.focus(),d()}),z=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(I.current),D.current=new Date().getTime(),I.current=window.setTimeout(B,e))},[B]);n.useEffect(()=>{let e=R.viewport;if(e){let t=()=>{z(k.current),m?.()},r=()=>{let e=new Date().getTime()-D.current;k.current=k.current-e,window.clearTimeout(I.current),p?.()};return e.addEventListener(C,r),e.addEventListener(N,t),()=>{e.removeEventListener(C,r),e.removeEventListener(N,t)}}},[R.viewport,A,p,m,z]),n.useEffect(()=>{c&&!R.isClosePausedRef.current&&z(A)},[c,A,R.isClosePausedRef,z]),n.useEffect(()=>(F(),()=>V()),[F,V]);let H=n.useMemo(()=>E?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(E):null,[E]);return R.viewport?(0,l.jsxs)(l.Fragment,{children:[H&&(0,l.jsx)(W,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:H}),(0,l.jsx)(U,{scope:r,onClose:B,children:i.createPortal((0,l.jsx)(_.ItemSlot,{scope:r,children:(0,l.jsx)(h.fC,{asChild:!0,onEscapeKeyDown:(0,o.M)(f,()=>{R.isFocusedToastEscapeKeyDownRef.current||B(),R.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,l.jsx)(y.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":R.swipeDirection,...w,ref:T,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.M)(e.onKeyDown,e=>{"Escape"!==e.key||(f?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(R.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:(0,o.M)(e.onPointerDown,e=>{0===e.button&&(O.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.M)(e.onPointerMove,e=>{if(!O.current)return;let t=e.clientX-O.current.x,r=e.clientY-O.current.y,n=!!M.current,i=["left","right"].includes(R.swipeDirection),o=["left","up"].includes(R.swipeDirection)?Math.min:Math.max,a=i?o(0,t):0,s=i?0:o(0,r),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};n?(M.current=u,q("toast.swipeMove",b,c,{discrete:!1})):Z(u,R.swipeDirection,l)?(M.current=u,q("toast.swipeStart",v,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(O.current=null)}),onPointerUp:(0,o.M)(e.onPointerUp,e=>{let t=M.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),M.current=null,O.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Z(t,R.swipeDirection,R.swipeThreshold)?q("toast.swipeEnd",P,n,{discrete:!0}):q("toast.swipeCancel",x,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),R.viewport)})]}):null}),W=e=>{let{__scopeToast:t,children:r,...i}=e,o=j(L,t),[a,s]=n.useState(!1),[u,c]=n.useState(!1);return function(e=()=>{}){let t=(0,g.W)(e);(0,b.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,l.jsx)(p.h,{asChild:!0,children:(0,l.jsx)(x.T,{...i,children:a&&(0,l.jsxs)(l.Fragment,{children:[o.label," ",r]})})})},z=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,l.jsx)(y.WV.div,{...n,ref:t})});z.displayName="ToastTitle";var H=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,l.jsx)(y.WV.div,{...n,ref:t})});H.displayName="ToastDescription";var $="ToastAction",G=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,l.jsx)(Y,{altText:r,asChild:!0,children:(0,l.jsx)(X,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${$}\`. Expected non-empty \`string\`.`),null)});G.displayName=$;var K="ToastClose",X=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=V(K,r);return(0,l.jsx)(Y,{asChild:!0,children:(0,l.jsx)(y.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,i.onClose)})})});X.displayName=K;var Y=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,l.jsx)(y.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function q(e,t,r,{discrete:n}){let i=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,y.jH)(i,o):i.dispatchEvent(o)}var Z=(e,t,r=0)=>{let n=Math.abs(e.x),i=Math.abs(e.y),o=n>i;return"left"===t||"right"===t?o&&n>r:!o&&i>r};function Q(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var J=O,ee=D,et=F,er=z,en=H,ei=G,eo=X},5049:(e,t,r)=>{r.d(t,{W:()=>i});var n=r(7577);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},2067:(e,t,r)=>{r.d(t,{T:()=>o});var n=r(7577),i=r(5049);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,a=n.useRef(o),s=(0,i.W)(t);return n.useEffect(()=>{a.current!==o&&(s(o),a.current=o)},[o,a,s]),r}({defaultProp:t,onChange:r}),s=void 0!==e,l=s?e:o,u=(0,i.W)(r);return[l,n.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else a(t)},[s,e,a,u])]}},5819:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(7577),i=globalThis?.document?n.useLayoutEffect:()=>{}},6009:(e,t,r)=>{r.d(t,{T:()=>a,f:()=>s});var n=r(7577),i=r(7335),o=r(326),a=n.forwardRef((e,t)=>(0,o.jsx)(i.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var s=a},8285:(e,t,r)=>{function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n,_class_private_field_loose_base:()=>n})},8817:(e,t,r)=>{r.r(t),r.d(t,{_:()=>i,_class_private_field_loose_key:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},1174:(e,t,r)=>{function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},8374:(e,t,r)=>{function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i,_interop_require_wildcard:()=>i})},9360:(e,t,r)=>{r.d(t,{j:()=>a});var n=r(1135);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.W,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(n);return a[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1135:(e,t,r)=>{r.d(t,{W:()=>n});function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n)}return i}(e))&&(n&&(n+=" "),n+=t);return n}},6933:(e,t,r)=>{r.d(t,{oO:()=>o});var n=r(7577),i=r(295);function o(e=!0){let t=(0,n.useContext)(i.O);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:o,register:a}=t,s=(0,n.useId)();(0,n.useEffect)(()=>{e&&a(s)},[e]);let l=(0,n.useCallback)(()=>e&&o&&o(s),[s,o,e]);return!r&&o?[!1,l]:[!0]}},339:(e,t,r)=>{r.d(t,{p:()=>n});let n=(0,r(7577).createContext)({})},3965:(e,t,r)=>{r.d(t,{_:()=>n});let n=(0,r(7577).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},295:(e,t,r)=>{r.d(t,{O:()=>n});let n=(0,r(7577).createContext)(null)},58:(e,t,r)=>{let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}r.d(t,{E:()=>om});let o=e=>Array.isArray(e);function a(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function s(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function u(e,t,r,n){if("function"==typeof t){let[i,o]=l(n);t=t(void 0!==r?r:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=l(n);t=t(void 0!==r?r:e.custom,i,o)}return t}function c(e,t,r){let n=e.getProps();return u(n,t,void 0!==r?r:n.custom,e)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],f=["initial",...d];function h(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=h(()=>void 0!==window.ScrollTimeline);class m{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e,t){let r=this.animations.map(r=>p()&&r.attachTimeline?r.attachTimeline(e):"function"==typeof t?t(r):void 0);return()=>{r.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class y extends m{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function g(e,t){return e?e[t]||e.default||e:void 0}function v(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function b(e){return"function"==typeof e}function x(e,t){e.timeline=t,e.onfinish=null}let P=e=>Array.isArray(e)&&"number"==typeof e[0],_={linearEasing:void 0},w=function(e,t){let r=h(e);return()=>{var e;return null!==(e=_[t])&&void 0!==e?e:r()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),R=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},E=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=e(R(0,i-1,t))+", ";return`linear(${n.substring(0,n.length-2)})`},S=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,T={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:S([0,.65,.55,1]),circOut:S([.55,0,1,.45]),backIn:S([.31,.01,.66,-.59]),backOut:S([.33,1.53,.69,.99])},j={x:!1,y:!1};function O(e,t){let r=function(e,t,r){if(e instanceof Element)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function M(e){return t=>{"touch"===t.pointerType||j.x||j.y||e(t)}}let A=(e,t)=>!!t&&(e===t||A(e,t.parentElement)),C=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,N=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),D=new WeakSet;function k(e){return t=>{"Enter"===t.key&&e(t)}}function I(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let L=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=k(()=>{if(D.has(r))return;I(r,"down");let e=k(()=>{I(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>I(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function F(e){return C(e)&&!(j.x||j.y)}let U=e=>1e3*e,V=e=>e/1e3,B=e=>e,W=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],z=new Set(W),H=new Set(["width","height","top","left","right","bottom",...W]),$=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),G=e=>o(e)?e[e.length-1]||0:e,K={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function Y(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,a=X.reduce((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,n=!1,i=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function s(t){o.has(t)&&(l.schedule(t),e()),t(a)}let l={schedule:(e,i=!1,a=!1)=>{let s=a&&n?t:r;return i&&o.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{if(a=e,n){i=!0;return}n=!0,[t,r]=[r,t],t.forEach(s),t.clear(),n=!1,i&&(i=!1,l.process(e))}};return l}(o),e),{}),{read:s,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:f}=a,h=()=>{let o=K.useManualTiming?i.timestamp:performance.now();r=!1,i.delta=n?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),d.process(i),f.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(h))},p=()=>{r=!0,n=!0,i.isProcessing||e(h)};return{schedule:X.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||p(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<X.length;t++)a[X[t]].cancel(e)},state:i,steps:a}}let{schedule:q,cancel:Z,state:Q,steps:J}=Y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);function ee(){n=void 0}let et={now:()=>(void 0===n&&et.set(Q.isProcessing||K.useManualTiming?Q.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(ee)}};function er(e,t){-1===e.indexOf(t)&&e.push(t)}function en(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class ei{constructor(){this.subscriptions=[]}add(e){return er(this.subscriptions,e),()=>en(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let eo=e=>!isNaN(parseFloat(e)),ea={current:void 0};class es{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=et.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=eo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ei);let r=this.events[e].add(t);return"change"===e?()=>{r(),q.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ea.current&&ea.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new es(e,t)}let eu=e=>!!(e&&e.getVelocity);function ec(e,t){let r=e.getValue("willChange");if(eu(r)&&r.add)return r.add(t)}let ed=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ef="data-"+ed("framerAppearId"),eh={current:!1},ep=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function em(e,t,r,n){if(e===t&&r===n)return B;let i=t=>(function(e,t,r,n,i){let o,a;let s=0;do(o=ep(a=t+(r-t)/2,n,i)-e)>0?r=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:ep(i(e),t,n)}let ey=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eg=e=>t=>1-e(1-t),ev=em(.33,1.53,.69,.99),eb=eg(ev),ex=ey(eb),eP=e=>(e*=2)<1?.5*eb(e):.5*(2-Math.pow(2,-10*(e-1))),e_=e=>1-Math.sin(Math.acos(e)),ew=eg(e_),eR=ey(e_),eE=e=>/^0[^.\s]+$/u.test(e),eS=(e,t,r)=>r>t?t:r<e?e:r,eT={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},ej={...eT,transform:e=>eS(0,1,e)},eO={...eT,default:1},eM=e=>Math.round(1e5*e)/1e5,eA=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eN=(e,t)=>r=>!!("string"==typeof r&&eC.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),eD=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,o,a,s]=n.match(eA);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},ek=e=>eS(0,255,e),eI={...eT,transform:e=>Math.round(ek(e))},eL={test:eN("rgb","red"),parse:eD("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+eI.transform(e)+", "+eI.transform(t)+", "+eI.transform(r)+", "+eM(ej.transform(n))+")"},eF={test:eN("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:eL.transform},eU=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eV=eU("deg"),eB=eU("%"),eW=eU("px"),ez=eU("vh"),eH=eU("vw"),e$={...eB,parse:e=>eB.parse(e)/100,transform:e=>eB.transform(100*e)},eG={test:eN("hsl","hue"),parse:eD("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+eB.transform(eM(t))+", "+eB.transform(eM(r))+", "+eM(ej.transform(n))+")"},eK={test:e=>eL.test(e)||eF.test(e)||eG.test(e),parse:e=>eL.test(e)?eL.parse(e):eG.test(e)?eG.parse(e):eF.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eL.transform(e):eG.transform(e)},eX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eY="number",eq="color",eZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eQ(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],o=0,a=t.replace(eZ,e=>(eK.test(e)?(n.color.push(o),i.push(eq),r.push(eK.parse(e))):e.startsWith("var(")?(n.var.push(o),i.push("var"),r.push(e)):(n.number.push(o),i.push(eY),r.push(parseFloat(e))),++o,"${}")).split("${}");return{values:r,split:a,indexes:n,types:i}}function eJ(e){return eQ(e).values}function e0(e){let{split:t,types:r}=eQ(e),n=t.length;return e=>{let i="";for(let o=0;o<n;o++)if(i+=t[o],void 0!==e[o]){let t=r[o];t===eY?i+=eM(e[o]):t===eq?i+=eK.transform(e[o]):i+=e[o]}return i}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,r;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(eA))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(eX))||void 0===r?void 0:r.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e5=new Set(["brightness","contrast","saturate","opacity"]);function e7(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(eA)||[];if(!n)return e;let i=r.replace(n,""),o=e5.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}let e3=/\b([a-z-]*)\(.*?\)/gu,e9={...e2,getAnimatableNone:e=>{let t=e.match(e3);return t?t.map(e7).join(" "):e}},e4={...eT,transform:Math.round},e6={borderWidth:eW,borderTopWidth:eW,borderRightWidth:eW,borderBottomWidth:eW,borderLeftWidth:eW,borderRadius:eW,radius:eW,borderTopLeftRadius:eW,borderTopRightRadius:eW,borderBottomRightRadius:eW,borderBottomLeftRadius:eW,width:eW,maxWidth:eW,height:eW,maxHeight:eW,top:eW,right:eW,bottom:eW,left:eW,padding:eW,paddingTop:eW,paddingRight:eW,paddingBottom:eW,paddingLeft:eW,margin:eW,marginTop:eW,marginRight:eW,marginBottom:eW,marginLeft:eW,backgroundPositionX:eW,backgroundPositionY:eW,rotate:eV,rotateX:eV,rotateY:eV,rotateZ:eV,scale:eO,scaleX:eO,scaleY:eO,scaleZ:eO,skew:eV,skewX:eV,skewY:eV,distance:eW,translateX:eW,translateY:eW,translateZ:eW,x:eW,y:eW,z:eW,perspective:eW,transformPerspective:eW,opacity:ej,originX:e$,originY:e$,originZ:eW,zIndex:e4,size:eW,fillOpacity:ej,strokeOpacity:ej,numOctaves:e4},e8={...e6,color:eK,backgroundColor:eK,outlineColor:eK,fill:eK,stroke:eK,borderColor:eK,borderTopColor:eK,borderRightColor:eK,borderBottomColor:eK,borderLeftColor:eK,filter:e9,WebkitFilter:e9},te=e=>e8[e];function tt(e,t){let r=te(e);return r!==e9&&(r=e2),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let tr=new Set(["auto","none","0"]),tn=e=>e===eT||e===eW,ti=(e,t)=>parseFloat(e.split(", ")[t]),to=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/u);if(i)return ti(i[1],t);{let t=n.match(/^matrix\((.+)\)$/u);return t?ti(t[1],e):0}},ta=new Set(["x","y","z"]),ts=W.filter(e=>!ta.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:to(4,13),y:to(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let tu=new Set,tc=!1,td=!1;function tf(){if(td){let e=Array.from(tu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return ts.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{var n;null===(n=e.getValue(t))||void 0===n||n.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}td=!1,tc=!1,tu.forEach(e=>e.complete()),tu.clear()}function th(){tu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(td=!0)})}class tp{constructor(e,t,r,n,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tu.add(this),tc||(tc=!0,q.read(th),q.resolveKeyframes(tf))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;for(let i=0;i<e.length;i++)if(null===e[i]){if(0===i){let i=null==n?void 0:n.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,o);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=o),n&&void 0===i&&n.set(e[0])}else e[i]=e[i-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ty=e=>t=>"string"==typeof t&&t.startsWith(e),tg=ty("--"),tv=ty("var(--"),tb=e=>!!tv(e)&&tx.test(e.split("/*")[0].trim()),tx=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tP=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,t_=e=>t=>t.test(e),tw=[eT,eW,eB,eV,eH,ez,{test:e=>"auto"===e,parse:e=>e}],tR=e=>tw.find(t_(e));class tE extends tp{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&tb(n=n.trim())){let i=function e(t,r,n=1){B(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=tP.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${null!=r?r:n}`,i]}(t);if(!i)return;let a=window.getComputedStyle(r).getPropertyValue(i);if(a){let e=a.trim();return tm(e)?parseFloat(e):e}return tb(o)?e(o,r,n+1):o}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!H.has(r)||2!==e.length)return;let[n,i]=e,o=tR(n),a=tR(i);if(o!==a){if(tn(o)&&tn(a))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||eE(n))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!tr.has(t)&&eQ(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=tt(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){var e;let{element:t,name:r,unresolvedKeyframes:n}=this;if(!t||!t.current)return;let i=t.getValue(r);i&&i.jump(this.measuredOrigin,!1);let o=n.length-1,a=n[o];n[o]=tl[r](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let tS=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tT=e=>null!==e;function tj(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(tT),o=t&&"loop"!==r&&t%2==1?0:i.length-1;return o&&void 0!==n?n:i[o]}class tO{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(th(),tf()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:r,type:n,velocity:i,delay:o,onComplete:a,onUpdate:s,isGenerator:l}=this.options;if(!l&&!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=tS(i,t),s=tS(o,t);return B(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||b(r))&&n)}(e,r,n,i)){if(eh.current||!o){s&&s(tj(e,this.options,t)),a&&a(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tM=(e,t,r)=>e+(t-e)*r;function tA(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function tC(e,t){return r=>r>0?t:e}let tN=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},tD=[eF,eL,eG],tk=e=>tD.find(t=>t.test(e));function tI(e){let t=tk(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===eG&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=tA(s,n,e+1/3),o=tA(s,n,e),a=tA(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let tL=(e,t)=>{let r=tI(e),n=tI(t);if(!r||!n)return tC(e,t);let i={...r};return e=>(i.red=tN(r.red,n.red,e),i.green=tN(r.green,n.green,e),i.blue=tN(r.blue,n.blue,e),i.alpha=tM(r.alpha,n.alpha,e),eL.transform(i))},tF=(e,t)=>r=>t(e(r)),tU=(...e)=>e.reduce(tF),tV=new Set(["none","hidden"]);function tB(e,t){return r=>tM(e,t,r)}function tW(e){return"number"==typeof e?tB:"string"==typeof e?tb(e)?tC:eK.test(e)?tL:t$:Array.isArray(e)?tz:"object"==typeof e?eK.test(e)?tL:tH:tC}function tz(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>tW(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function tH(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=tW(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let t$=(e,t)=>{let r=e2.createTransformer(t),n=eQ(e),i=eQ(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?tV.has(e)&&!i.values.length||tV.has(t)&&!n.values.length?function(e,t){return tV.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):tU(tz(function(e,t){var r;let n=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){let a=t.types[o],s=e.indexes[a][i[a]],l=null!==(r=e.values[s])&&void 0!==r?r:0;n[o]=l,i[a]++}return n}(n,i),i.values),r):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(e,t))};function tG(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?tM(e,t,r):tW(e)(e,t)}function tK(e,t,r){var n,i;let o=Math.max(t-5,0);return n=r-e(o),(i=t-o)?1e3/i*n:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(e,t){return e*Math.sqrt(1-t*t)}let tq=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tX.visualDuration,t=tX.bounce){let r;let n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=n,a=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:f,velocity:h,isResolvedFromDuration:p}=function(e){let t={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...e};if(!tQ(e,tZ)&&tQ(e,tq)){if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*eS(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:tX.mass,stiffness:n,damping:i}}else{let r=function({duration:e=tX.duration,bounce:t=tX.bounce,velocity:r=tX.velocity,mass:n=tX.mass}){let i,o;B(e<=U(tX.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=eS(tX.minDamping,tX.maxDamping,a),e=eS(tX.minDuration,tX.maxDuration,V(e)),a<1?(i=t=>{let n=t*a,i=n*e;return .001-(n-r)/tY(t,a)*Math.exp(-i)},o=t=>{let n=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=tY(Math.pow(t,2),a);return(n*r+r-o)*Math.exp(-n)*(-i(t)+.001>0?-1:1)/s}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),o=t=>e*e*(r-t)*Math.exp(-t*e));let s=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,5/e);if(e=U(e),isNaN(s))return{stiffness:tX.stiffness,damping:tX.damping,duration:e};{let t=Math.pow(s,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:tX.mass}).isResolvedFromDuration=!0}}return t}({...n,velocity:-V(n.velocity||0)}),m=h||0,y=c/(2*Math.sqrt(u*d)),g=s-a,b=V(Math.sqrt(u/d)),x=5>Math.abs(g);if(i||(i=x?tX.restSpeed.granular:tX.restSpeed.default),o||(o=x?tX.restDelta.granular:tX.restDelta.default),y<1){let e=tY(b,y);r=t=>s-Math.exp(-y*b*t)*((m+y*b*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===y)r=e=>s-Math.exp(-b*e)*(g+(m+b*g)*e);else{let e=b*Math.sqrt(y*y-1);r=t=>{let r=Math.exp(-y*b*t),n=Math.min(e*t,300);return s-r*((m+y*b*g)*Math.sinh(n)+e*g*Math.cosh(n))/e}}let P={calculatedDuration:p&&f||null,next:e=>{let t=r(e);if(p)l.done=e>=f;else{let n=0;y<1&&(n=0===e?U(m):tK(r,e,t));let a=Math.abs(n)<=i,u=Math.abs(s-t)<=o;l.done=a&&u}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(v(P),2e4),t=E(t=>P.next(e*t).value,e,30);return e+"ms "+t}};return P}function t0({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,f;let h=e[0],p={done:!1,value:h},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,y=e=>void 0===s?l:void 0===l?s:Math.abs(s-e)<Math.abs(l-e)?s:l,g=r*t,v=h+g,b=void 0===a?v:a(v);b!==v&&(g=b-h);let x=e=>-g*Math.exp(-e/n),P=e=>b+x(e),_=e=>{let t=x(e),r=P(e);p.done=Math.abs(t)<=u,p.value=p.done?b:r},w=e=>{m(p.value)&&(d=e,f=tJ({keyframes:[p.value,y(p.value)],velocity:tK(P,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(f||void 0!==d||(t=!0,_(e),w(e)),void 0!==d&&e>=d)?f.next(e-d):(t||_(e),p)}}}let t1=em(.42,0,1,1),t2=em(0,0,.58,1),t5=em(.42,0,.58,1),t7=e=>Array.isArray(e)&&"number"!=typeof e[0],t3={linear:B,easeIn:t1,easeInOut:t5,easeOut:t2,circIn:e_,circInOut:eR,circOut:ew,backIn:eb,backInOut:ex,backOut:ev,anticipate:eP},t9=e=>{if(P(e)){B(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return em(t,r,n,i)}return"string"==typeof e?(B(void 0!==t3[e],`Invalid easing type '${e}'`),t3[e]):e};function t4({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=t7(n)?n.map(t9):t9(n),o={done:!1,value:t[0]},a=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if(B(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,r){let n=[],i=r||tG,o=e.length-1;for(let r=0;r<o;r++){let o=i(e[r],e[r+1]);t&&(o=tU(Array.isArray(t)?t[r]||B:t,o)),n.push(o)}return n}(t,n,i),l=s.length,u=r=>{if(a&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=R(e[n],e[n+1],r);return s[n](i)};return r?t=>u(eS(e[0],e[o-1],t)):u}((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=R(0,t,n);e.push(tM(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||t5).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}let t6=e=>{let t=({timestamp:t})=>e(t);return{start:()=>q.update(t,!0),stop:()=>Z(t),now:()=>Q.isProcessing?Q.timestamp:et.now()}},t8={decay:t0,inertia:t0,tween:t4,keyframes:t4,spring:tJ},re=e=>e/100;class rt extends tO{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:r,element:n,keyframes:i}=this.options,o=(null==n?void 0:n.KeyframeResolver)||tp;this.resolver=new o(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,r;let{type:n="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:a,velocity:s=0}=this.options,l=b(n)?n:t8[n]||t4;l!==t4&&"number"!=typeof e[0]&&(t=tU(re,tG(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===a&&(r=l({...this.options,keyframes:[...e].reverse(),velocity:-s})),null===u.calculatedDuration&&(u.calculatedDuration=v(u));let{calculatedDuration:c}=u,d=c+o;return{generator:u,mirroredGenerator:r,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:d,totalDuration:d*(i+1)-o}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:r}=this;if(!r){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:n,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:s,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=r;if(null===this.startTime)return i.next(0);let{delay:d,repeat:f,repeatType:h,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let y=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?y<0:y>u;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,b=i;if(f){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,f+1))%2&&("reverse"===h?(r=1-r,p&&(r-=p/c)):"mirror"===h&&(b=o)),v=eS(0,1,r)*c}let x=g?{done:!1,value:s[0]}:b.next(v);a&&(x.value=a(x.value));let{done:P}=x;g||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let _=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return _&&void 0!==n&&(x.value=tj(s,this.options,n)),m&&m(x.value),_&&this.finish(),x}get duration(){let{resolved:e}=this;return e?V(e.calculatedDuration):0}get time(){return V(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=V(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t6,onPlay:t,startTime:r}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=r?r:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let rr=new Set(["opacity","clipPath","filter","transform"]),rn=h(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ri={anticipate:eP,backInOut:ex,circInOut:eR};class ro extends tO{constructor(e){super(e);let{name:t,motionValue:r,element:n,keyframes:i}=this.options;this.resolver=new tE(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}initPlayback(e,t){var r;let{duration:n=300,times:i,ease:o,type:a,motionValue:s,name:l,startTime:u}=this.options;if(!s.owner||!s.owner.current)return!1;if("string"==typeof o&&w()&&o in ri&&(o=ri[o]),b((r=this.options).type)||"spring"===r.type||!function e(t){return!!("function"==typeof t&&w()||!t||"string"==typeof t&&(t in T||w())||P(t)||Array.isArray(t)&&t.every(e))}(r.ease)){let{onComplete:t,onUpdate:r,motionValue:s,element:l,...u}=this.options,c=function(e,t){let r=new rt({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:e[0]},i=[],o=0;for(;!n.done&&o<2e4;)i.push((n=r.sample(o)).value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),n=c.duration,i=c.times,o=c.ease,a="keyframes"}let c=function(e,t,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeInOut",times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t,r){if(t)return"function"==typeof t&&w()?E(t,r):P(t)?S(t):Array.isArray(t)?t.map(t=>e(t,r)||T.easeOut):T[t]}(s,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"})}(s.owner.current,l,e,{...this.options,duration:n,times:i,ease:o});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(x(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:r}=this.options;s.set(tj(e,this.options,t)),r&&r(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:a,ease:o,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return V(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return V(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.currentTime=U(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return B;let{animation:r}=t;x(r,e)}else this.pendingTimeline=e;return B}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:r,duration:n,type:i,ease:o,times:a}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:s,element:l,...u}=this.options,c=new rt({...u,keyframes:r,duration:n,type:i,ease:o,times:a,isGenerator:!0}),d=U(this.time);e.setWithVelocity(c.sample(d-10).value,c.sample(d).value,10)}let{onStop:s}=this.options;s&&s(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:o,type:a}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return rn()&&r&&rr.has(r)&&!s&&!l&&!n&&"mirror"!==i&&0!==o&&"inertia"!==a}}let ra={type:"spring",stiffness:500,damping:25,restSpeed:10},rs=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(e,{keyframes:t})=>t.length>2?rl:z.has(e)?e.startsWith("scale")?rs(t[1]):ra:ru,rd=(e,t,r,n={},i,o)=>a=>{let s=g(n,e)||{},l=s.delay||n.delay||0,{elapsed:u=0}=n;u-=U(l);let c={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&(c={...c,...rc(e,c)}),c.duration&&(c.duration=U(c.duration)),c.repeatDelay&&(c.repeatDelay=U(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(d=!0)),(eh.current||K.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),d&&!o&&void 0!==t.get()){let e=tj(c.keyframes,s);if(void 0!==e)return q.update(()=>{c.onUpdate(e),c.onComplete()}),new y([])}return!o&&ro.supports(c)?new ro(c):new rt(c)};function rf(e,t,{delay:r=0,transitionOverride:n,type:i}={}){var o;let{transition:a=e.getDefaultTransition(),transitionEnd:s,...l}=t;n&&(a=n);let u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in l){let n=e.getValue(t,null!==(o=e.latestValues[t])&&void 0!==o?o:null),i=l[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(d,t))continue;let s={delay:r,...g(a||{},t)},c=!1;if(window.MotionHandoffAnimation){let r=e.props[ef];if(r){let e=window.MotionHandoffAnimation(r,t,q);null!==e&&(s.startTime=e,c=!0)}}ec(e,t),n.start(rd(t,n,i,e.shouldReduceMotion&&H.has(t)?{type:!1}:s,e,c));let f=n.animation;f&&u.push(f)}return s&&Promise.all(u).then(()=>{q.update(()=>{s&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=c(e,t)||{};for(let t in i={...i,...r}){let r=G(i[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,el(r))}}(e,s)})}),u}function rh(e,t,r={}){var n;let i=c(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:o=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(o=r.transitionOverride);let a=i?()=>Promise.all(rf(e,i,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:i=0,staggerChildren:a,staggerDirection:s}=o;return function(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(rp).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(rh(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,i+n,a,s,r)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([a(),s(r.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function rp(e,t){return e.sortNodePosition(t)}let rm=f.length,ry=[...d].reverse(),rg=d.length;function rv(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rv(!0),whileInView:rv(),whileHover:rv(),whileTap:rv(),whileDrag:rv(),whileFocus:rv(),exit:rv()}}class rx{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rP extends rx{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>rh(e,t,r)));else if("string"==typeof t)n=rh(e,t,r);else{let i="function"==typeof t?c(e,t,r.custom):t;n=Promise.all(rf(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rb(),n=!0,l=t=>(r,n)=>{var i;let o=c(e,n,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(o){let{transition:e,transitionEnd:t,...n}=o;r={...r,...n,...t}}return r};function u(u){let{props:c}=e,d=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<rm;e++){let n=f[e],i=t.props[n];(s(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},h=[],p=new Set,m={},y=1/0;for(let t=0;t<rg;t++){var g;let f=ry[t],v=r[f],b=void 0!==c[f]?c[f]:d[f],x=s(b),P=f===u?v.isActive:null;!1===P&&(y=t);let _=b===d[f]&&b!==c[f]&&x;if(_&&n&&e.manuallyAnimateOnMount&&(_=!1),v.protectedKeys={...m},!v.isActive&&null===P||!b&&!v.prevProp||i(b)||"boolean"==typeof b)continue;let w=(g=v.prevProp,"string"==typeof b?b!==g:!!Array.isArray(b)&&!a(b,g)),R=w||f===u&&v.isActive&&!_&&x||t>y&&x,E=!1,S=Array.isArray(b)?b:[b],T=S.reduce(l(f),{});!1===P&&(T={});let{prevResolvedValues:j={}}=v,O={...j,...T},M=t=>{R=!0,p.has(t)&&(E=!0,p.delete(t)),v.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in O){let t=T[e],r=j[e];if(!m.hasOwnProperty(e))(o(t)&&o(r)?a(t,r):t===r)?void 0!==t&&p.has(e)?M(e):v.protectedKeys[e]=!0:null!=t?M(e):p.add(e)}v.prevProp=b,v.prevResolvedValues=T,v.isActive&&(m={...m,...T}),n&&e.blockInitialAnimation&&(R=!1);let A=!(_&&w)||E;R&&A&&h.push(...S.map(e=>({animation:e,options:{type:f}})))}if(p.size){let t={};p.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=null!=n?n:null}),h.push({animation:t})}let v=!!h.length;return n&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(v=!1),n=!1,v?t(h):Promise.resolve()}return{animateChanges:u,setActive:function(t,n){var i;if(r[t].isActive===n)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let o=u(t);for(let e in r)r[e].protectedKeys={};return o},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rb(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let r_=0;class rw extends rx{constructor(){super(...arguments),this.id=r_++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function rR(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function rE(e){return{point:{x:e.pageX,y:e.pageY}}}let rS=e=>t=>C(t)&&e(t,rE(t));function rT(e,t,r,n){return rR(e,t,rS(r),n)}let rj=(e,t)=>Math.abs(e-t);class rO{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rC(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rj(e.x,t.x)**2+rj(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=Q;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rM(t,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rC("pointercancel"===e.type?this.lastMoveEventInfo:rM(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!C(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=rM(rE(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=Q;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,rC(o,this.history)),this.removeListeners=tU(rT(this.contextWindow,"pointermove",this.handlePointerMove),rT(this.contextWindow,"pointerup",this.handlePointerUp),rT(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function rM(e,t){return t?{point:t(e.point)}:e}function rA(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rC({point:e},t){return{point:e,delta:rA(e,rN(t)),offset:rA(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rN(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>U(.1)));)r--;if(!n)return{x:0,y:0};let o=V(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function rN(e){return e[e.length-1]}function rD(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function rk(e){return e.max-e.min}function rI(e,t,r,n=.5){e.origin=n,e.originPoint=tM(t.min,t.max,e.origin),e.scale=rk(r)/rk(t),e.translate=tM(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rL(e,t,r,n){rI(e.x,t.x,r.x,n?n.originX:void 0),rI(e.y,t.y,r.y,n?n.originY:void 0)}function rF(e,t,r){e.min=r.min+t.min,e.max=e.min+rk(t)}function rU(e,t,r){e.min=t.min-r.min,e.max=e.min+rk(t)}function rV(e,t,r){rU(e.x,t.x,r.x),rU(e.y,t.y,r.y)}function rB(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rW(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function rz(e,t,r){return{min:rH(e,t),max:rH(e,r)}}function rH(e,t){return"number"==typeof e?e:e[t]||0}let r$=()=>({translate:0,scale:1,origin:0,originPoint:0}),rG=()=>({x:r$(),y:r$()}),rK=()=>({min:0,max:0}),rX=()=>({x:rK(),y:rK()});function rY(e){return[e("x"),e("y")]}function rq({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rZ(e){return void 0===e||1===e}function rQ({scale:e,scaleX:t,scaleY:r}){return!rZ(e)||!rZ(t)||!rZ(r)}function rJ(e){return rQ(e)||r0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function r0(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function r1(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function r2(e,t=0,r=1,n,i){e.min=r1(e.min,t,r,n,i),e.max=r1(e.max,t,r,n,i)}function r5(e,{x:t,y:r}){r2(e.x,t.translate,t.scale,t.originPoint),r2(e.y,r.translate,r.scale,r.originPoint)}function r7(e,t){e.min=e.min+t,e.max=e.max+t}function r3(e,t,r,n,i=.5){let o=tM(e.min,e.max,i);r2(e,t,r,o,n)}function r9(e,t){r3(e.x,t.x,t.scaleX,t.scale,t.originX),r3(e.y,t.y,t.scaleY,t.scale,t.originY)}function r4(e,t){return rq(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let r6=({current:e})=>e?e.ownerDocument.defaultView:null,r8=new WeakMap;class ne{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rX(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rO(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rE(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===r||"y"===r?j[r]?null:(j[r]=!0,()=>{j[r]=!1}):j.x||j.y?null:(j.x=j.y=!0,()=>{j.x=j.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rY(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eB.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=rk(n);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),i&&q.postRender(()=>i(e,t)),ec(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>rY(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:r6(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&q.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!nt(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tM(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tM(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&rD(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rB(e.x,r,i),y:rB(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rz(e,"left","right"),y:rz(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rY(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rD(t))return!1;let n=t.current;B(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=r4(e,r),{scroll:i}=t;return i&&(r7(n.x,i.offset.x),r7(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a={x:rW((e=i.layout.layoutBox).x,o.x),y:rW(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=rq(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(rY(a=>{if(!nt(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return ec(this.visualElement,e),r.start(rd(e,r,0,t,this.visualElement,!1))}stopAnimation(){rY(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rY(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rY(t=>{let{drag:r}=this.getProps();if(!nt(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-tM(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rD(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rY(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rk(e),i=rk(t);return i>n?r=R(t.min,t.max-n,e.min):n>i&&(r=R(e.min,e.max-i,t.min)),eS(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rY(t=>{if(!nt(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set(tM(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;r8.set(this.visualElement,this);let e=rT(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rD(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),q.read(t);let i=rR(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rY(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function nt(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class nr extends rx{constructor(e){super(e),this.removeGroupControls=B,this.removeListeners=B,this.controls=new ne(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}}let nn=e=>(t,r)=>{e&&q.postRender(()=>e(t,r))};class ni extends rx{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(e){this.session=new rO(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:r6(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:nn(e),onStart:nn(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&q.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=rT(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var no,na,ns=r(326),nl=r(7577),nu=r(6933),nc=r(339);let nd=(0,nl.createContext)({}),nf={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nh(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let np={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!eW.test(e))return e;e=parseFloat(e)}let r=nh(e,t.target.x),n=nh(e,t.target.y);return`${r}% ${n}%`}},nm={},{schedule:ny,cancel:ng}=Y(queueMicrotask,!1);class nv extends nl.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;Object.assign(nm,nx),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nf.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||q.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ny.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nb(e){let[t,r]=(0,nu.oO)(),n=(0,nl.useContext)(nc.p);return(0,ns.jsx)(nv,{...e,layoutGroup:n,switchLayoutGroup:(0,nl.useContext)(nd),isPresent:t,safeToRemove:r})}let nx={borderRadius:{...np,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:np,borderTopRightRadius:np,borderBottomLeftRadius:np,borderBottomRightRadius:np,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=e2.parse(e);if(n.length>5)return e;let i=e2.createTransformer(e),o="number"!=typeof n[0]?1:0,a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=tM(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}},nP=(e,t)=>e.depth-t.depth;class n_{constructor(){this.children=[],this.isDirty=!1}add(e){er(this.children,e),this.isDirty=!0}remove(e){en(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nP),this.isDirty=!1,this.children.forEach(e)}}function nw(e){let t=eu(e)?e.get():e;return $(t)?t.toValue():t}let nR=["TopLeft","TopRight","BottomLeft","BottomRight"],nE=nR.length,nS=e=>"string"==typeof e?parseFloat(e):e,nT=e=>"number"==typeof e||eW.test(e);function nj(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nO=nA(0,.5,ew),nM=nA(.5,.95,B);function nA(e,t,r){return n=>n<e?0:n>t?1:r(R(e,t,n))}function nC(e,t){e.min=t.min,e.max=t.max}function nN(e,t){nC(e.x,t.x),nC(e.y,t.y)}function nD(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nk(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nI(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(eB.test(t)&&(t=parseFloat(t),t=tM(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=tM(o.min,o.max,n);e===o&&(s-=t),e.min=nk(e.min,t,r,s,i),e.max=nk(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let nL=["x","scaleX","originX"],nF=["y","scaleY","originY"];function nU(e,t,r,n){nI(e.x,t,nL,r?r.x:void 0,n?n.x:void 0),nI(e.y,t,nF,r?r.y:void 0,n?n.y:void 0)}function nV(e){return 0===e.translate&&1===e.scale}function nB(e){return nV(e.x)&&nV(e.y)}function nW(e,t){return e.min===t.min&&e.max===t.max}function nz(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nH(e,t){return nz(e.x,t.x)&&nz(e.y,t.y)}function n$(e){return rk(e.x)/rk(e.y)}function nG(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nK{constructor(){this.members=[]}add(e){er(this.members,e),e.scheduleRender()}remove(e){if(en(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nX={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nY="undefined"!=typeof window&&void 0!==window.MotionDebug,nq=["","X","Y","Z"],nZ={visibility:"hidden"},nQ=0;function nJ(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function n0({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=nQ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nY&&(nX.totalNodes=nX.resolvedTargetDeltas=nX.recalculatedProjection=0),this.nodes.forEach(n5),this.nodes.forEach(ie),this.nodes.forEach(it),this.nodes.forEach(n7),nY&&window.MotionDebug.record(nX)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new n_)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ei),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=et.now(),n=({timestamp:t})=>{let i=t-r;i>=250&&(Z(n),e(i-250))};return q.read(n,!0),()=>Z(n)}(n,250),nf.hasAnimatedSinceResize&&(nf.hasAnimatedSinceResize=!1,this.nodes.forEach(n8))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||il,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=o.getProps(),l=!this.targetLayout||!nH(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...g(i,"layout"),onPlay:a,onComplete:s};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||n8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ir),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[ef];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",q,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n9);return}this.isUpdating||this.nodes.forEach(n4),this.isUpdating=!1,this.nodes.forEach(n6),this.nodes.forEach(n1),this.nodes.forEach(n2),this.clearAllSnapshots();let e=et.now();Q.delta=eS(0,1e3/60,e-Q.timestamp),Q.timestamp=e,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ny.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n3),this.sharedNodes.forEach(ii)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rX(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nB(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&(t||rJ(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),id((t=n).x),id(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return rX();let r=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(ip))){let{scroll:e}=this.root;e&&(r7(r.x,e.offset.x),r7(r.y,e.offset.y))}return r}removeElementScroll(e){var t;let r=rX();if(nN(r,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return r;for(let t=0;t<this.path.length;t++){let n=this.path[t],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nN(r,e),r7(r.x,i.offset.x),r7(r.y,i.offset.y))}return r}applyTransform(e,t=!1){let r=rX();nN(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&r9(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rJ(n.latestValues)&&r9(r,n.latestValues)}return rJ(this.latestValues)&&r9(r,this.latestValues),r}removeTransform(e){let t=rX();nN(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rJ(r.latestValues))continue;rQ(r.latestValues)&&r.updateSnapshot();let n=rX();nN(n,r.measurePageBox()),nU(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rJ(this.latestValues)&&nU(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==o;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:l}=this.options;if(this.layout&&(s||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rX(),this.relativeTargetOrigin=rX(),rV(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rX(),this.targetWithTransforms=rX()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rF(r.x,n.x,i.x),rF(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nN(this.target,this.layout.layoutBox),r5(this.target,this.targetDelta)):nN(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rX(),this.relativeTargetOrigin=rX(),rV(this.relativeTargetOrigin,this.target,e.target),nN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nY&&nX.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rQ(this.parent.latestValues)||r0(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nN(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;(function(e,t,r,n=!1){let i,o;let a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&r9(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,r5(e,o)),n&&rJ(i.latestValues)&&r9(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=rX());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nD(this.prevProjectionDelta.x,this.projectionDelta.x),nD(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rL(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&nG(this.projectionDelta.x,this.prevProjectionDelta.x)&&nG(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nY&&nX.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rG(),this.projectionDelta=rG(),this.projectionDeltaWithTransform=rG()}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=rG();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=rX(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(is));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(io(a.x,e.x,n),io(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,h,p;rV(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,ia(h.x,p.x,s.x,n),ia(h.y,p.y,s.y,n),r&&(u=this.relativeTarget,f=r,nW(u.x,f.x)&&nW(u.y,f.y))&&(this.isProjectionDirty=!1),r||(r=rX()),nN(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=tM(0,void 0!==r.opacity?r.opacity:1,nO(n)),e.opacityExit=tM(void 0!==t.opacity?t.opacity:1,0,nM(n))):o&&(e.opacity=tM(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nE;i++){let o=`border${nR[i]}Radius`,a=nj(t,o),s=nj(r,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||nT(a)===nT(s)?(e[o]=Math.max(tM(nS(a),nS(s),n),0),(eB.test(s)||eB.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||r.rotate)&&(e.rotate=tM(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{nf.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=eu(0)?0:el(0);return n.start(rd("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ih(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rX();let t=rk(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rk(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nN(t,r),r9(t,i),rL(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nK),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&nJ("z",e,n,this.animationValues);for(let t=0;t<nq.length;t++)nJ(`rotate${nq[t]}`,e,n,this.animationValues),nJ(`skew${nq[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nZ;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nw(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nw(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rJ(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let a=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=function(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=(null==r?void 0:r.z)||0;if((i||o||a)&&(n=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),a&&(n+=`skewX(${a}deg) `),s&&(n+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(n+=`scale(${s}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,a),i&&(n.transform=i(a,n.transform));let{x:s,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:n.opacity=o===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,nm){if(void 0===a[e])continue;let{correct:t,applyTo:r}=nm[e],i="none"===n.transform?a[e]:t(a[e],o);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=i}else n[e]=i}return this.options.layoutId&&(n.pointerEvents=o===this?nw(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n9),this.root.sharedNodes.clear()}}}function n1(e){e.updateLayout()}function n2(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,o=r.source!==e.layout.source;"size"===i?rY(e=>{let n=o?r.measuredBox[e]:r.layoutBox[e],i=rk(n);n.min=t[e].min,n.max=n.min+i}):ih(i,r.layoutBox,t)&&rY(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],a=rk(t[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=rG();rL(a,t,r.layoutBox);let s=rG();o?rL(s,e.applyTransform(n,!0),r.measuredBox):rL(s,t,r.layoutBox);let l=!nB(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=rX();rV(a,r.layoutBox,i.layoutBox);let s=rX();rV(s,t,o.layoutBox),nH(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n5(e){nY&&nX.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n7(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n3(e){e.clearSnapshot()}function n9(e){e.clearMeasurements()}function n4(e){e.isLayoutDirty=!1}function n6(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ie(e){e.resolveTargetDelta()}function it(e){e.calcProjection()}function ir(e){e.resetSkewAndRotation()}function ii(e){e.removeLeadSnapshot()}function io(e,t,r){e.translate=tM(t.translate,0,r),e.scale=tM(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function ia(e,t,r,n){e.min=tM(t.min,r.min,n),e.max=tM(t.max,r.max,n)}function is(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let il={duration:.45,ease:[.4,0,.1,1]},iu=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ic=iu("applewebkit/")&&!iu("chrome/")?Math.round:B;function id(e){e.min=ic(e.min),e.max=ic(e.max)}function ih(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(n$(t)-n$(r)))}function ip(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let im=n0({attachResizeListener:(e,t)=>rR(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iy={current:void 0},ig=n0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!iy.current){let e=new im({});e.mount(window),e.setOptions({layoutScroll:!0}),iy.current=e}return iy.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function iv(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&q.postRender(()=>i(t,rE(t)))}class ib extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=O(e,r),a=M(e=>{let{target:r}=e,n=t(e);if("function"!=typeof n||!r)return;let o=M(e=>{n(e),r.removeEventListener("pointerleave",o)});r.addEventListener("pointerleave",o,i)});return n.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,e=>(iv(this.node,e,"Start"),e=>iv(this.node,e,"End"))))}unmount(){}}class ix extends rx{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tU(rR(this.node.current,"focus",()=>this.onFocus()),rR(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function iP(e,t,r){let{props:n}=e;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&q.postRender(()=>i(t,rE(t)))}class i_ extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=O(e,r),a=e=>{let n=e.currentTarget;if(!F(e)||D.has(n))return;D.add(n);let o=t(e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),F(e)&&D.has(n)&&(D.delete(n),"function"==typeof o&&o(e,{success:t}))},s=e=>{a(e,r.useGlobalTarget||A(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{N.has(e.tagName)||-1!==e.tabIndex||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),e.addEventListener("focus",e=>L(e,i),i)}),o}(e,e=>(iP(this.node,e,"Start"),(e,{success:t})=>iP(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,iR=new WeakMap,iE=e=>{let t=iw.get(e.target);t&&t(e)},iS=e=>{e.forEach(iE)},iT={some:0,all:1};class ij extends rx{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iT[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;iR.has(r)||iR.set(r,{});let n=iR.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iS,{root:e,...t})),n[i]}(t);return iw.set(e,r),n.observe(e),()=>{iw.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iO=(0,nl.createContext)({strict:!1});var iM=r(3965);let iA=(0,nl.createContext)({});function iC(e){return i(e.animate)||f.some(t=>s(e[t]))}function iN(e){return!!(iC(e)||e.variants)}function iD(e){return Array.isArray(e)?e.join(" "):e}var ik=r(8263);let iI={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in iI)iL[e]={isEnabled:t=>iI[e].some(e=>!!t[e])};let iF=Symbol.for("motionComponentSymbol");var iU=r(295),iV=r(2482);let iB=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iW(e){if("string"!=typeof e||e.includes("-"));else if(iB.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var iz=r(4749);let iH=e=>(t,r)=>{let n=(0,nl.useContext)(iA),o=(0,nl.useContext)(iU.O),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:r},n,o,a){let s={latestValues:function(e,t,r,n){let o={},a=n(e,{});for(let e in a)o[e]=nw(a[e]);let{initial:s,animate:l}=e,c=iC(e),d=iN(e);t&&d&&!c&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===l&&(l=t.animate));let f=!!r&&!1===r.initial,h=(f=f||!1===s)?l:s;if(h&&"boolean"!=typeof h&&!i(h)){let t=Array.isArray(h)?h:[h];for(let r=0;r<t.length;r++){let n=u(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=f?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,o,a,e),renderState:t()};return r&&(s.onMount=e=>r({props:n,current:e,...s}),s.onUpdate=e=>r(e)),s})(e,t,n,o);return r?a():(0,iz.h)(a)},i$=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iG={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iK=W.length;function iX(e,t,r){let{style:n,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let r=t[e];if(z.has(e)){a=!0;continue}if(tg(e)){i[e]=r;continue}{let t=i$(r,e6[e]);e.startsWith("origin")?(s=!0,o[e]=t):n[e]=t}}if(!t.transform&&(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let o=0;o<iK;o++){let a=W[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===(a.startsWith("scale")?1:0):0===parseFloat(s))||r){let e=i$(s,e6[a]);if(!l){i=!1;let t=iG[a]||a;n+=`${t}(${e}) `}r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let iY={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iZ(e,t,r){return"string"==typeof e?e:eW.transform(t+r*e)}function iQ(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:o,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...u},c,d){if(iX(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:f,style:h,dimensions:p}=e;f.transform&&(p&&(h.transform=f.transform),delete f.transform),p&&(void 0!==i||void 0!==o||h.transform)&&(h.transformOrigin=function(e,t,r){let n=iZ(t,e.x,e.width),i=iZ(r,e.y,e.height);return`${n} ${i}`}(p,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(f.x=t),void 0!==r&&(f.y=r),void 0!==n&&(f.scale=n),void 0!==a&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?iY:iq;e[o.offset]=eW.transform(-n);let a=eW.transform(t),s=eW.transform(r);e[o.array]=`${a} ${s}`}(f,a,s,l,!1)}let iJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),i0=()=>({...iJ(),attrs:{}}),i1=e=>"string"==typeof e&&"svg"===e.toLowerCase();function i2(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}let i5=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function i7(e,t,r,n){for(let r in i2(e,t,void 0,n),t.attrs)e.setAttribute(i5.has(r)?r:ed(r),t.attrs[r])}function i3(e,{layout:t,layoutId:r}){return z.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!nm[e]||"opacity"===e)}function i9(e,t,r){var n;let{style:i}=e,o={};for(let a in i)(eu(i[a])||t.style&&eu(t.style[a])||i3(a,e)||(null===(n=null==r?void 0:r.getValue(a))||void 0===n?void 0:n.liveStyle)!==void 0)&&(o[a]=i[a]);return o}function i4(e,t,r){let n=i9(e,t,r);for(let r in e)(eu(e[r])||eu(t[r]))&&(n[-1!==W.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let i6=["x","y","width","height","cx","cy","r"],i8={useVisualState:iH({scrapeMotionValuesFromProps:i4,createRenderState:i0,onUpdate:({props:e,prevProps:t,current:r,renderState:n,latestValues:i})=>{if(!r)return;let o=!!e.drag;if(!o){for(let e in i)if(z.has(e)){o=!0;break}}if(!o)return;let a=!t;if(t)for(let r=0;r<i6.length;r++){let n=i6[r];e[n]!==t[n]&&(a=!0)}a&&q.read(()=>{(function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}})(r,n),q.render(()=>{iQ(n,i,i1(r.tagName),e.transformTemplate),i7(r,n)})})}})},oe={useVisualState:iH({scrapeMotionValuesFromProps:i9,createRenderState:iJ})};function ot(e,t,r){for(let n in t)eu(t[n])||i3(n,r)||(e[n]=t[n])}let or=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function on(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||or.has(e)}let oi=e=>!on(e);try{!function(e){e&&(oi=t=>t.startsWith("on")?!on(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let oo={current:null},oa={current:!1},os=[...tw,eK,e2],ol=e=>os.find(t_(e)),ou=new WeakMap,oc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class od{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,q.render(this.render,!1,!0))};let{latestValues:s,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=iC(t),this.isVariantNode=iN(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==s[e]&&eu(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ou.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oa.current||function(){if(oa.current=!0,ik.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oo.current=e.matches;e.addListener(t),t()}else oo.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in ou.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=z.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&q.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rX()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<oc.length;t++){let r=oc[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],o=r[n];if(eu(i))e.addValue(n,i);else if(eu(o))e.addValue(n,el(i,{owner:e}));else if(o!==i){if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,el(void 0!==t?t:i,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=el(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){var r;let n=void 0===this.latestValues[e]&&this.current?null!==(r=this.getBaseTargetFromProps(this.props,e))&&void 0!==r?r:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(tm(n)||eE(n))?n=parseFloat(n):!ol(n)&&e2.test(t)&&(n=tt(e,t)),this.setBaseTarget(e,eu(n)?n.get():n)),eu(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let r;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let i=u(this.props,n,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);i&&(r=i[e])}if(n&&void 0!==r)return r;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||eu(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new ei),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class of extends od{constructor(){super(...arguments),this.KeyframeResolver=tE}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eu(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class oh extends of{constructor(){super(...arguments),this.type="html",this.renderInstance=i2}readValueFromInstance(e,t){if(z.has(t)){let e=te(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(tg(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return r4(e,t)}build(e,t,r){iX(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i9(e,t,r)}}class op extends of{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rX}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(z.has(t)){let e=te(t);return e&&e.default||0}return t=i5.has(t)?t:ed(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return i4(e,t,r)}build(e,t,r){iQ(e,t,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,r,n){i7(e,t,r,n)}mount(e){this.isSVGTag=i1(e.tagName),super.mount(e)}}let om=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((no={animation:{Feature:rP},exit:{Feature:rw},inView:{Feature:ij},tap:{Feature:i_},focus:{Feature:ix},hover:{Feature:ib},pan:{Feature:ni},drag:{Feature:nr,ProjectionNode:ig,MeasureLayout:nb},layout:{ProjectionNode:ig,MeasureLayout:nb}},na=(e,t)=>iW(e)?new op(t):new oh(t,{allowProjection:e!==nl.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){var o,a;function l(e,o){var a;let l;let u={...(0,nl.useContext)(iM._),...e,layoutId:function({layoutId:e}){let t=(0,nl.useContext)(nc.p).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:c}=u,d=function(e){let{initial:t,animate:r}=function(e,t){if(iC(e)){let{initial:t,animate:r}=e;return{initial:!1===t||s(t)?t:void 0,animate:s(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,nl.useContext)(iA));return(0,nl.useMemo)(()=>({initial:t,animate:r}),[iD(t),iD(r)])}(e),f=n(e,c);if(!c&&ik.j){(0,nl.useContext)(iO).strict;let e=function(e){let{drag:t,layout:r}=iL;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);l=e.MeasureLayout,d.visualElement=function(e,t,r,n,i){var o,a;let{visualElement:s}=(0,nl.useContext)(iA),l=(0,nl.useContext)(iO),u=(0,nl.useContext)(iU.O),c=(0,nl.useContext)(iM._).reducedMotion,d=(0,nl.useRef)(null);n=n||l.renderer,!d.current&&n&&(d.current=n(e,{visualState:t,parent:s,props:r,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let f=d.current,h=(0,nl.useContext)(nd);f&&!f.projection&&i&&("html"===f.type||"svg"===f.type)&&function(e,t,r,n){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&rD(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(d.current,r,i,h);let p=(0,nl.useRef)(!1);(0,nl.useInsertionEffect)(()=>{f&&p.current&&f.update(r,u)});let m=r[ef],y=(0,nl.useRef)(!!m&&!(null===(o=window.MotionHandoffIsComplete)||void 0===o?void 0:o.call(window,m))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,m)));return(0,iV.L)(()=>{f&&(p.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),ny.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),(0,nl.useEffect)(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,m)}),y.current=!1))}),f}(i,f,u,t,e.ProjectionNode)}return(0,ns.jsxs)(iA.Provider,{value:d,children:[l&&d.visualElement?(0,ns.jsx)(l,{visualElement:d.visualElement,...u}):null,r(i,e,(a=d.visualElement,(0,nl.useCallback)(e=>{e&&f.onMount&&f.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):rD(o)&&(o.current=e))},[a])),f,c,d.visualElement)]})}e&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof i?i:`create(${null!==(a=null!==(o=i.displayName)&&void 0!==o?o:i.name)&&void 0!==a?a:""})`}`;let u=(0,nl.forwardRef)(l);return u[iF]=i,u}({...iW(e)?i8:oe,preloadedFeatures:no,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=(iW(t)?function(e,t,r,n){let i=(0,nl.useMemo)(()=>{let r=i0();return iQ(r,t,i1(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};ot(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return ot(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,nl.useMemo)(()=>{let r=iJ();return iX(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,t),s=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(oi(i)||!0===r&&on(i)||!t&&!on(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==nl.Fragment?{...s,...a,ref:n}:{},{children:u}=r,c=(0,nl.useMemo)(()=>eu(u)?u.get():u,[u]);return(0,nl.createElement)(t,{...l,children:c})}}(t),createVisualElement:na,Component:e})}))},8263:(e,t,r)=>{r.d(t,{j:()=>n});let n="undefined"!=typeof window},4749:(e,t,r)=>{r.d(t,{h:()=>i});var n=r(7577);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2482:(e,t,r)=>{r.d(t,{L:()=>i});var n=r(7577);let i=r(8263).j?n.useLayoutEffect:n.useEffect},9664:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(7577);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:s="",children:l,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:o("lucide",s),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...a},l)=>(0,n.createElement)(s,{ref:l,iconNode:t,className:o(`lucide-${i(e)}`,r),...a}));return r.displayName=`${e}`,r}},3020:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(9664).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1009:(e,t,r)=>{r.d(t,{m6:()=>ed});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?i(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){l(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t;let r=[],n=0,i=0,o=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===i){if(":"===s){r.push(e.slice(o,a)),o=a+1;continue}if("/"===s){t=a;continue}}"["===s?n++:"]"===s?n--:"("===s?i++:")"===s&&i--}let a=0===r.length?e:e.substring(o),s=h(a);return{modifiers:r,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:p(e),...n(e)}),y=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:o}=t,a=[],s=e.trim().split(y),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:h}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!h,m=n(p?f.substring(0,h):f);if(!m){if(!p||!(m=n(f))){l=t+(l.length>0?" "+l:l);continue}p=!1}let y=o(c).join(":"),g=d?y+"!":y,v=g+m;if(a.includes(v))continue;a.push(v);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];a.push(g+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},P=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,_=/^\((?:(\w[\w-]*):)?(.+)\)$/i,w=/^\d+\/\d+$/,R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>w.test(e),M=e=>!!e&&!Number.isNaN(Number(e)),A=e=>!!e&&Number.isInteger(Number(e)),C=e=>e.endsWith("%")&&M(e.slice(0,-1)),N=e=>R.test(e),D=()=>!0,k=e=>E.test(e)&&!S.test(e),I=()=>!1,L=e=>T.test(e),F=e=>j.test(e),U=e=>!B(e)&&!K(e),V=e=>ee(e,ea,I),B=e=>P.test(e),W=e=>ee(e,es,k),z=e=>ee(e,el,M),H=e=>ee(e,er,I),$=e=>ee(e,ei,F),G=e=>ee(e,I,L),K=e=>_.test(e),X=e=>et(e,es),Y=e=>et(e,eu),q=e=>et(e,er),Z=e=>et(e,ea),Q=e=>et(e,ei),J=e=>et(e,ec,!0),ee=(e,t,r)=>{let n=P.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=_.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e,en=new Set(["image","url"]),ei=e=>en.has(e),eo=new Set(["length","size","percentage"]),ea=e=>eo.has(e),es=e=>"length"===e,el=e=>"number"===e,eu=e=>"family-name"===e,ec=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...t){let r,n,i;let o=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,o=a,a(s)};function a(e){let t=n(e);if(t)return t;let o=g(e,r);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=x("color"),t=x("font"),r=x("text"),n=x("font-weight"),i=x("tracking"),o=x("leading"),a=x("breakpoint"),s=x("container"),l=x("spacing"),u=x("radius"),c=x("shadow"),d=x("inset-shadow"),f=x("drop-shadow"),h=x("blur"),p=x("perspective"),m=x("aspect"),y=x("ease"),g=x("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],P=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto","contain","none"],w=()=>[O,"px","full","auto",K,B,l],R=()=>[A,"none","subgrid",K,B],E=()=>["auto",{span:["full",A,K,B]},K,B],S=()=>[A,"auto",K,B],T=()=>["auto","min","max","fr",K,B],j=()=>[K,B,l],k=()=>["start","end","center","between","around","evenly","stretch","baseline"],I=()=>["start","end","center","stretch"],L=()=>[K,B,l],F=()=>["px",...L()],ee=()=>["px","auto",...L()],et=()=>[O,"auto","px","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",K,B,l],er=()=>[e,K,B],en=()=>[C,W],ei=()=>["","none","full",u,K,B],eo=()=>["",M,X,W],ea=()=>["solid","dashed","dotted","double"],es=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],el=()=>["","none",h,K,B],eu=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",K,B],ec=()=>["none",M,K,B],ed=()=>["none",M,K,B],ef=()=>[M,K,B],eh=()=>[O,"full","px",K,B,l];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[D],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:[M],text:[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",O,B,K,m]}],container:["container"],columns:[{columns:[M,B,K,s]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...b(),B,K]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:_()}],"overscroll-x":[{"overscroll-x":_()}],"overscroll-y":[{"overscroll-y":_()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:w()}],"inset-x":[{"inset-x":w()}],"inset-y":[{"inset-y":w()}],start:[{start:w()}],end:[{end:w()}],top:[{top:w()}],right:[{right:w()}],bottom:[{bottom:w()}],left:[{left:w()}],visibility:["visible","invisible","collapse"],z:[{z:[A,"auto",K,B]}],basis:[{basis:[O,"full","auto",K,B,s,l]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[M,O,"auto","initial","none",B]}],grow:[{grow:["",M,K,B]}],shrink:[{shrink:["",M,K,B]}],order:[{order:[A,"first","last","none",K,B]}],"grid-cols":[{"grid-cols":R()}],"col-start-end":[{col:E()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":R()}],"row-start-end":[{row:E()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":T()}],"auto-rows":[{"auto-rows":T()}],gap:[{gap:j()}],"gap-x":[{"gap-x":j()}],"gap-y":[{"gap-y":j()}],"justify-content":[{justify:[...k(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...k()]}],"align-items":[{items:[...I(),"baseline"]}],"align-self":[{self:["auto",...I(),"baseline"]}],"place-content":[{"place-content":k()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:F()}],px:[{px:F()}],py:[{py:F()}],ps:[{ps:F()}],pe:[{pe:F()}],pt:[{pt:F()}],pr:[{pr:F()}],pb:[{pb:F()}],pl:[{pl:F()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":L()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":L()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen",...et()]}],"min-h":[{"min-h":["screen","none",...et()]}],"max-h":[{"max-h":["screen",...et()]}],"font-size":[{text:["base",r,X,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,K,z]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",C,B]}],"font-family":[{font:[Y,B,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,K,B]}],"line-clamp":[{"line-clamp":[M,"none",K,z]}],leading:[{leading:[K,B,o,l]}],"list-image":[{"list-image":["none",K,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",K,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ea(),"wavy"]}],"text-decoration-thickness":[{decoration:[M,"from-font","auto",K,W]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[M,"auto",K,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:["px",...L()]}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...b(),q,H]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Z,V]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},A,K,B],radial:["",K,B],conic:[A,K,B]},Q,$]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:eo()}],"border-w-x":[{"border-x":eo()}],"border-w-y":[{"border-y":eo()}],"border-w-s":[{"border-s":eo()}],"border-w-e":[{"border-e":eo()}],"border-w-t":[{"border-t":eo()}],"border-w-r":[{"border-r":eo()}],"border-w-b":[{"border-b":eo()}],"border-w-l":[{"border-l":eo()}],"divide-x":[{"divide-x":eo()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":eo()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ea(),"hidden","none"]}],"divide-style":[{divide:[...ea(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ea(),"none","hidden"]}],"outline-offset":[{"outline-offset":[M,K,B]}],"outline-w":[{outline:["",M,X,W]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,J,G]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",K,B,d]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:eo()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[M,W]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":eo()}],"inset-ring-color":[{"inset-ring":er()}],opacity:[{opacity:[M,K,B]}],"mix-blend":[{"mix-blend":[...es(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":es()}],filter:[{filter:["","none",K,B]}],blur:[{blur:el()}],brightness:[{brightness:[M,K,B]}],contrast:[{contrast:[M,K,B]}],"drop-shadow":[{"drop-shadow":["","none",f,K,B]}],grayscale:[{grayscale:["",M,K,B]}],"hue-rotate":[{"hue-rotate":[M,K,B]}],invert:[{invert:["",M,K,B]}],saturate:[{saturate:[M,K,B]}],sepia:[{sepia:["",M,K,B]}],"backdrop-filter":[{"backdrop-filter":["","none",K,B]}],"backdrop-blur":[{"backdrop-blur":el()}],"backdrop-brightness":[{"backdrop-brightness":[M,K,B]}],"backdrop-contrast":[{"backdrop-contrast":[M,K,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",M,K,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[M,K,B]}],"backdrop-invert":[{"backdrop-invert":["",M,K,B]}],"backdrop-opacity":[{"backdrop-opacity":[M,K,B]}],"backdrop-saturate":[{"backdrop-saturate":[M,K,B]}],"backdrop-sepia":[{"backdrop-sepia":["",M,K,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":L()}],"border-spacing-x":[{"border-spacing-x":L()}],"border-spacing-y":[{"border-spacing-y":L()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",K,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[M,"initial",K,B]}],ease:[{ease:["linear","initial",y,K,B]}],delay:[{delay:[M,K,B]}],animate:[{animate:["none",g,K,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,K,B]}],"perspective-origin":[{"perspective-origin":eu()}],rotate:[{rotate:ec()}],"rotate-x":[{"rotate-x":ec()}],"rotate-y":[{"rotate-y":ec()}],"rotate-z":[{"rotate-z":ec()}],scale:[{scale:ed()}],"scale-x":[{"scale-x":ed()}],"scale-y":[{"scale-y":ed()}],"scale-z":[{"scale-z":ed()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[K,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:eu()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K,B]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[M,X,W,z]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}})},3370:(e,t,r)=>{function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})}};