(()=>{var e={};e.id=809,e.ids=[809],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},3285:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(1279),s(9977),s(5866);var r=s(3191),a=s(8716),i=s(7922),n=s.n(i),o=s(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["live-sets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1279)),"/Users/<USER>/Workspace/auspex/auspex-website/src/app/live-sets/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,9977)),"/Users/<USER>/Workspace/auspex/auspex-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Workspace/auspex/auspex-website/src/app/live-sets/page.tsx"],p="/live-sets/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/live-sets/page",pathname:"/live-sets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1332:(e,t,s)=>{Promise.resolve().then(s.bind(s,991))},991:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var r=s(326),a=s(7577),i=s(1223);let n=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));o.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var l=s(58),d=s(9813);let c={offscreen:{y:50,opacity:0},onscreen:{y:0,opacity:1,transition:{type:"spring",bounce:.4,duration:.8}}};function p({set:e}){let{title:t,artist:s,youtubeVideoId:a,date:i,description:p}=e,x=`https://www.youtube.com/embed/${a}`;return r.jsx(l.E.div,{variants:c,children:(0,r.jsxs)(n,{className:"flex flex-col bg-card/50 backdrop-blur-sm transition-all duration-300 hover:shadow-primary/20 hover:shadow-lg hover:border-primary/30 h-full overflow-hidden",children:[r.jsx("div",{className:"p-4 aspect-video relative group",children:r.jsx(d.D,{url:x})}),(0,r.jsxs)(o,{className:"flex flex-col flex-grow p-4 pt-0",children:[r.jsx("div",{className:"text-sm text-muted-foreground mb-2",children:new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",timeZone:"UTC"})}),r.jsx("h3",{className:"font-headline text-2xl text-primary mb-1",children:t}),r.jsx("h4",{className:"font-headline text-lg text-foreground/80 mb-4",children:s}),r.jsx("p",{className:"text-foreground/80 flex-grow",children:p})]})]})})}function x({sets:e}){return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8 pt-28",children:[(0,r.jsxs)(l.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:[r.jsx("h1",{className:"text-4xl lg:text-5xl font-headline mb-8 text-center text-primary",children:"Live Sets"}),r.jsx("p",{className:"text-center text-lg text-foreground/70 max-w-2xl mx-auto mb-12",children:"Experience the energy of Auspex Records artists, live from festivals and sessions around the world."})]}),r.jsx(l.E.div,{className:"grid grid-cols-1 gap-12",initial:"offscreen",whileInView:"onscreen",viewport:{once:!0,amount:.1},transition:{staggerChildren:.3},children:e.map(e=>r.jsx(p,{set:e},e.id))})]})}},1279:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(9510),a=s(6961);let i=(0,s(8570).createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/sets/live-sets-client.tsx#default`);async function n(){let e=await (0,a.Eh)();return r.jsx(i,{sets:e})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,864,621,539],()=>s(3285));module.exports=r})();