(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},4121:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(150),s(9977),s(5866);var a=s(3191),r=s(8716),i=s(7922),n=s.n(i),o=s(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,150)),"/Users/<USER>/Workspace/auspex/auspex-website/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,9977)),"/Users/<USER>/Workspace/auspex/auspex-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Workspace/auspex/auspex-website/src/app/page.tsx"],u="/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2328:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},5261:(e,t,s)=>{Promise.resolve().then(s.bind(s,2534)),Promise.resolve().then(s.bind(s,422))},5321:(e,t,s)=>{Promise.resolve().then(s.bind(s,5147))},5147:(e,t,s)=>{"use strict";s.d(t,{default:()=>k});var a=s(326),r=s(434),i=s(58),n=s(1664),o=s(9664);let l=(0,o.Z)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);var d=s(342);let c=(0,o.Z)("Twitch",[["path",{d:"M21 2H3v16h5v4l4-4h5l4-4V2zm-10 9V7m5 4V7",key:"c0yzno"}]]);var u=s(2631),p=s(6226),x=s(1223);let h=({className:e})=>a.jsx(p.default,{src:"/logos/White_ink_transparent_background.png",alt:"Auspex Records Logo",width:100,height:100,className:(0,x.cn)("w-auto",e),priority:!0});var m=s(2445),f=s(645);let v={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.05,delayChildren:.1}}},g={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5,ease:"easeOut"}}},b={hidden:{opacity:0,scale:.5},visible:{opacity:1,scale:1,transition:{duration:.5,ease:[.22,1,.36,1]}}},y={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.5,delay:.1,ease:"easeOut"}}},w="Independent label committed to curating and cultivating",j="future-facing sound.",N={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.02,delayChildren:.2}}},_={hidden:{opacity:0,y:10},visible:{opacity:1,y:0}};function k(){return(0,a.jsxs)("section",{className:"relative h-screen w-full flex flex-col items-center justify-center text-center overflow-hidden",children:[a.jsx(m.Z,{}),a.jsx("div",{className:"absolute inset-0 -z-10 w-full h-full overflow-hidden",children:a.jsx("div",{className:"absolute inset-0 w-full h-full bg-cover bg-center",style:{backgroundImage:"url(/wallpapers/Auspex_SantaCruz.png)"}})}),a.jsx("div",{className:"absolute inset-0 bg-background/60"}),(0,a.jsxs)(i.E.div,{className:"relative z-10 p-4 flex flex-col items-center flex-grow justify-center",variants:v,initial:"hidden",animate:"visible",children:[a.jsx(i.E.div,{variants:b,children:a.jsx(h,{className:"h-24 md:h-40 w-auto mb-4"})}),a.jsx(i.E.h1,{className:"text-5xl md:text-8xl font-headline text-white mb-6 tracking-wider font-sans",variants:y,children:"Auspex Records"}),(0,a.jsxs)(i.E.div,{className:"text-xl md:text-2xl max-w-3xl mx-auto text-primary mb-10 font-light",variants:N,children:[a.jsx("p",{className:"mb-2","aria-label":w,children:w.split("").map((e,t)=>a.jsx(i.E.span,{variants:_,style:{display:"inline-block",whiteSpace:"pre"},children:e},`line1-${t}`))}),a.jsx("p",{"aria-label":j,children:j.split("").map((e,t)=>a.jsx(i.E.span,{variants:_,style:{display:"inline-block",whiteSpace:"pre"},children:e},`line2-${t}`))})]}),(0,a.jsxs)(i.E.div,{className:"flex gap-4 justify-center",variants:g,children:[a.jsx(n.z,{asChild:!0,size:"lg",variant:"outline",className:"font-bold text-lg bg-white/5 border-white/20 text-white hover:bg-white/10 shadow-lg transition-all duration-300 transform hover:scale-105",children:a.jsx(r.default,{href:"/releases",children:"Explore Releases"})}),a.jsx(n.z,{asChild:!0,size:"lg",variant:"outline",className:"font-bold text-lg bg-white/5 border-white/20 text-white hover:bg-white/10 shadow-lg transition-all duration-300 transform hover:scale-105",children:a.jsx(r.default,{href:"/live-sets",children:"Watch Live Sets"})})]}),a.jsx(i.E.div,{className:"flex flex-wrap items-center justify-center gap-8 mt-12",variants:g,children:(0,a.jsxs)(u.pn,{children:[(0,a.jsxs)(u.u,{children:[a.jsx(u.aJ,{asChild:!0,children:a.jsx("a",{href:"https://www.instagram.com/auspex_records/",target:"_blank",rel:"noopener noreferrer",className:"text-white/70 hover:text-primary transition-colors transform hover:scale-110",children:a.jsx(l,{className:"h-7 w-7"})})}),a.jsx(u._v,{side:"bottom",className:"bg-black/50 border-white/10 text-white",children:a.jsx("p",{children:"Instagram"})})]}),(0,a.jsxs)(u.u,{children:[a.jsx(u.aJ,{asChild:!0,children:a.jsx("a",{href:"https://youtube.com/@AuspexRecords",target:"_blank",rel:"noopener noreferrer",className:"text-white/70 hover:text-primary transition-colors transform hover:scale-110",children:a.jsx(d.Z,{className:"h-7 w-7"})})}),a.jsx(u._v,{side:"bottom",className:"bg-black/50 border-white/10 text-white",children:a.jsx("p",{children:"YouTube"})})]}),(0,a.jsxs)(u.u,{children:[a.jsx(u.aJ,{asChild:!0,children:a.jsx("a",{href:"https://www.twitch.tv/auspexrecords",target:"_blank",rel:"noopener noreferrer",className:"text-white/70 hover:text-primary transition-colors transform hover:scale-110",children:a.jsx(c,{className:"h-7 w-7"})})}),a.jsx(u._v,{side:"bottom",className:"bg-black/50 border-white/10 text-white",children:a.jsx("p",{children:"Twitch"})})]})]})})]},"hero-content"),a.jsx("div",{className:"w-full absolute bottom-0",children:a.jsx(f.Z,{})})]})}},645:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(326);function r(){return a.jsx("footer",{className:"bg-transparent",children:a.jsx("div",{className:"container mx-auto px-4 py-6",children:a.jsx("div",{className:"text-center text-white/40 text-sm",children:(0,a.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," Auspex Records. All rights reserved."]})})})})}},2445:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(326),r=s(434),i=s(5047),n=s(1223),o=s(58);function l(){let e=(0,i.usePathname)();return a.jsx(o.E.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,ease:"easeOut"},className:"fixed top-0 left-0 right-0 z-50",children:a.jsx("div",{className:"container mx-auto flex h-20 items-center justify-center px-6",children:a.jsx("nav",{className:"flex items-center gap-2 rounded-full p-2 bg-black/30 backdrop-blur-lg border border-white/10 shadow-lg",children:[{href:"/",label:"Home"},{href:"/releases",label:"Releases"},{href:"/live-sets",label:"Live Sets"}].map(t=>(0,a.jsxs)(r.default,{href:t.href,className:(0,n.cn)("relative text-md font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full"),children:[t.label,(e===t.href||e===t.href+"/")&&a.jsx(o.E.div,{className:"absolute inset-0 bg-primary/20 rounded-full -z-10",layoutId:"active-link",transition:{type:"spring",stiffness:500,damping:30,mass:.8}})]},t.href))})})})}},2534:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(326),r=s(5047),i=s(2445),n=s(645);function o({children:e}){let t="/"===(0,r.usePathname)();return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"aurora-bg"}),(0,a.jsxs)("div",{className:"relative flex min-h-screen w-full flex-col",children:[!t&&a.jsx(i.Z,{}),a.jsx("main",{className:"flex-1",children:e}),!t&&a.jsx(n.Z,{})]})]})}},1664:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var a=s(326),r=s(7577),i=s(9469),n=s(9360),o=s(1223);let l=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 px-3",lg:"h-11 px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...n},d)=>{let c=r?i.g7:"button";return a.jsx(c,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},422:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>v});var a=s(326),r=s(4097),i=s(7577),n=s(4559),o=s(9360),l=s(3020),d=s(1223);let c=n.zt,u=i.forwardRef(({className:e,...t},s)=>a.jsx(n.l_,{ref:s,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=n.l_.displayName;let p=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=i.forwardRef(({className:e,variant:t,...s},r)=>a.jsx(n.fC,{ref:r,className:(0,d.cn)(p({variant:t}),e),...s}));x.displayName=n.fC.displayName,i.forwardRef(({className:e,...t},s)=>a.jsx(n.aU,{ref:s,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.aU.displayName;let h=i.forwardRef(({className:e,...t},s)=>a.jsx(n.x8,{ref:s,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:a.jsx(l.Z,{className:"h-4 w-4"})}));h.displayName=n.x8.displayName;let m=i.forwardRef(({className:e,...t},s)=>a.jsx(n.Dx,{ref:s,className:(0,d.cn)("text-sm font-semibold",e),...t}));m.displayName=n.Dx.displayName;let f=i.forwardRef(({className:e,...t},s)=>a.jsx(n.dk,{ref:s,className:(0,d.cn)("text-sm opacity-90",e),...t}));function v(){let{toasts:e}=(0,r.pm)();return(0,a.jsxs)(c,{children:[e.map(function({id:e,title:t,description:s,action:r,...i}){return(0,a.jsxs)(x,{...i,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&a.jsx(m,{children:t}),s&&a.jsx(f,{children:s})]}),r,a.jsx(h,{})]},e)}),a.jsx(u,{})]})}f.displayName=n.dk.displayName},2631:(e,t,s)=>{"use strict";s.d(t,{_v:()=>c,aJ:()=>d,pn:()=>o,u:()=>l});var a=s(326),r=s(7577),i=s(9582),n=s(1223);let o=i.zt,l=i.fC,d=i.xz,c=r.forwardRef(({className:e,sideOffset:t=4,...s},r)=>a.jsx(i.VY,{ref:r,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s}));c.displayName=i.VY.displayName},4097:(e,t,s)=>{"use strict";s.d(t,{pm:()=>p});var a=s(7577);let r=0,i=new Map,n=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=a.useState(d);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},1223:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(1135),r=s(1009);function i(...e){return(0,r.m6)((0,a.W)(e))}},9977:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>o});var a=s(9510);s(5023);var r=s(8570);let i=(0,r.createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/ui/toaster.tsx#Toaster`),n=(0,r.createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/layout/layout-client.tsx#default`),o={title:"Auspex Records",description:"Independent label committed to curating and cultivating future-facing sound.",icons:{icon:[{url:"/favicon.ico",sizes:"32x32",type:"image/x-icon"},{url:"/icon-16x16.png",sizes:"16x16",type:"image/png"},{url:"/icon-32x32.png",sizes:"32x32",type:"image/png"},{url:"/icon-192x192.png",sizes:"192x192",type:"image/png"}],shortcut:"/favicon.ico"}};function l({children:e}){return a.jsx("html",{lang:"en",className:"dark",children:(0,a.jsxs)("body",{className:"antialiased",children:[a.jsx(n,{children:e}),a.jsx(i,{})]})})}},150:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(9510);let r=(0,s(8570).createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/home/<USER>"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(6621);let r=e=>[{type:"image/x-icon",sizes:"32x32",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[948,864,621,915],()=>s(4121));module.exports=a})();